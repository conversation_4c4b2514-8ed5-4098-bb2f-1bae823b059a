# 代码库结构

## 主要文件
- **main.py**: 主程序文件，包含所有核心功能
- **filter_config.txt**: 过滤配置文件，定义4个过滤集合的条件
- **token_aggregator.log**: 日志文件
- **telegram_*.session**: Telegram会话文件

## 核心功能模块

### 1. API客户端类
- **AxiomAPIClient**: Axiom API数据获取
- **AveApiClient**: Ave.ai API数据获取  
- **GmgnApiClient**: GMGN API数据获取（连接池轮换机制）

### 2. 数据处理函数
- **process_token()**: 主要的代币信息处理函数
- **get_*_info()**: 各种API数据获取函数
- **format_token_info()**: 格式化代币信息为Markdown

### 3. 过滤系统
- **load_filter_config()**: 加载过滤配置
- **parse_condition()**: 解析过滤条件
- **check_condition()**: 检查单个条件
- **check_filter_conditions()**: 检查所有过滤条件

### 4. 消息发送
- **send_to_telegram()**: 发送到群A
- **send_to_telegram_group_b()**: 发送到群B
- **extract_telegram_info()**: 从Telegram频道提取信息

### 5. 主要流程
- **subscribe_new_tokens()**: WebSocket订阅和主要处理流程
- **main()**: 程序入口点

## 配置系统
- 支持代理开关配置
- Telegram API配置
- 过滤条件配置文件
- 多实例运行支持