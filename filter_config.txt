# 功能1过滤配置文件
# 说明：满足任一集合的所有条件即转发到群B
# 集合内条件为AND关系，集合间为OR关系
# 支持的操作符：<=, >=, ==, !=, 区间匹配（如：0 <= 字段名 <= 18）

# 示例集合1 - 高时间低dev（支持null值匹配）
[集合1]
创建时间分钟差 >= 6
60 >= 前10持有者占比 >= 18.2
466 >= 持有者数量
600 >= 5分钟卖出次数
DexPaid支付时间 <= 77
7 >= 知名钱包
2 >= 鲸鱼钱包
77 >= 新钱包
17 >= 狙击手钱包
8.9 >= 最大持有者占比 >= 2.5
开发者持有占比 <= 0.2 
狙击手持有占比 <= 7.7 
6 <= 机器人用户数 
DEV /DEV <= 6 
Whale /巨鲸 <= 2 
"Insiders /老鼠仓" <= 6 
0 <= 内部人士持有占比 <= 17 
开发者代币总数 <= 777 
"Phishing /钓鱼地址" <= 7
2 <= "Cabal /阴谋集团" <= 7


# 示例集合2 - 高高（可以根据需要修改），3 <= 24H换手率，时间段: IN {0, 2, 3, 4, 5, 6, 7}
[集合2]
创建时间分钟差 >= 6
0.3 <= 开发者持有占比 <= 15
5分钟卖出次数 <= 377
19.7000 <= top 10占比 <= 47.9100
0.6600 <= 狙击手持有占比 <= 15.3300
顶级老鼠交易者占比 <= 2.1800
顶级打包者占比 <= 38.6100
DexPaid支付时间 <= 10
8 <= 顶级钱包
0 <= 1m Change
15 >= 最大持有者占比 >= 2.6

# 示例集合3 - 低低
[集合3]
5 >= 创建时间分钟差
开发者持有占比 <= 0.2
7 <= 5分钟卖出次数 <= 666
5分钟卖出次数 <= 600
前10持有者占比 >= 18.3
持有者数量 >= 2.0000 
机器人用户数: <= 277
顶级打包者占比 <= 57.7
37 >= 顶级钱包 >= 7
0.1800 <= 顶级老鼠交易者占比 <= 4.0900
开发者代币总数 <= 289
3.1 <= 最大持有者占比 <= 15
3 <= "Phishing /钓鱼地址"
4 <= "Cabal /阴谋集团"


# 示例集合4 - 低时高dev
[集合4]
5 >= 创建时间分钟差
0.3 <= 开发者持有占比 <= 15
600 >= 5分钟卖出次数 
前10持有者占比 >= 27
开发者持有占比 <= 10.1000
狙击手钱包 <= 20
机器人用户数 >= 17
4.4000 <= 最大持有者占比 <=15

# 配置说明：
# 1. 使用 [集合名] 定义新的过滤集合
# 2. 每行一个条件，支持多种格式：
#    - 正向格式：字段名 操作符 数值 (如：持有者数量 <= 466)
#    - 反向格式：数值 操作符 字段名 (如：466 >= 持有者数量)
# 3. 支持区间格式：最小值 <= 字段名 <= 最大值
# 4. 带空格或特殊字符的字段名用双引号包围
# 5. 使用 # 添加注释
# 6. 可以注释掉不需要的集合或条件
# 7. ⭐ null值处理规则：
#    - null值满足 <= 操作符的条件（如：Degen呼叫数 <= 7555）
#    - null值满足区间条件的上限（如：0 <= 字段名 <= 1000）
#    - null值不满足 >= 操作符的条件
#    - 这样可以同时匹配数值区间和null值的情况
# 8. ⭐ 反向格式说明：
#    - 466 >= 持有者数量 等价于 持有者数量 <= 466
#    - 100 <= 持有者数量 等价于 持有者数量 >= 100

# 完整可用字段名列表（基于实际电报群消息显示的字段名）：

# === 基础信息字段 ===
# - 代币地址, 名称, 符号, 描述, 创建者, 创建时间, 创建时间分钟差
# - 检测时间

# === Axiom指标字段 ===
# - 开发者代币总数, 已迁移代币数, 持有者数量, 机器人用户数
# - 前10大持有者占比, 开发者持有占比, 内部人士持有占比
# - 打包者持有占比, 狙击手持有占比, 总配对费用

# === Ave.ai标签统计字段 ===
# - "Cabal /阴谋集团", "Phishing /钓鱼地址", "Insiders /老鼠仓"
# - "Bundle /捆绑地址", "Snipers /狙击", "DEV /DEV"
# - "Whale /巨鲸", "Smarters /聪明钱", "KOL /KOL"
# - "Rug Pull统计", Insiders, Phishing, Cabal, Bundle, "All Tag Rate"

# === GMGN统计数据字段 ===
# - 持有者数量, 蓝筹持有者占比, 信号数量, Degen呼叫数
# - 顶级老鼠交易者占比, 顶级打包者占比, 顶级陷阱交易者占比
# - 平均持有余额, 前10持有者占比, 前100持有者占比

# === 钱包类型字段 ===
# - 智能钱包, 新钱包, 知名钱包, 创建者钱包, 狙击手钱包
# - 老鼠交易者钱包, 鲸鱼钱包, 顶级钱包, 关注钱包, 打包者钱包

# === 交易数据字段 ===
# - Twitter变更次数, Twitter删除帖子代币数, Twitter创建代币数
# - 创建者代币余额, DEX广告状态, CTO标志
# - 当前价格, 1分钟价格
# - 1分钟买入次数, 1分钟卖出次数, 1分钟交易量, 1分钟买入量, 1分钟卖出量
# - 5分钟买入次数, 5分钟卖出次数, 5分钟交易量, 5分钟买入量, 5分钟卖出量
# - 热度等级

# === 持有者行为数据字段 ===
# - hold, bought_more, transfered, bought_rate, holding_rate
# - 符合条件的钱包数量, 最大持有者占比
# - "native_balance >= 6000000000 的个数"
# - "tags 中包含 photon 的次数"
# - "transfer_in: true 出现次数", "is_new: true 出现次数"
# - "is_suspicious: true 出现次数"
# - "sandwich_bot 出现次数", "bundler 出现次数"

# === 其他信息字段 ===
# - 网站, Twitter, Telegram, DexPaid支付时间
# - 跑路概率, 猎人频道信息, 相关消息数量

# 注意：
# 1. 带空格或特殊字符的字段名需要用双引号包围
# 2. 现在的字段名完全对应电报消息中显示的文字（去掉emoji）
# 3. 例如：Degen呼叫数, 新钱包, bought_rate 等都可以直接使用

# ⚠️ 重要：重复字段名的区分使用方法
#
# 1. 持有者数量（已区分）：
#    - GMGN持有者数量 → GMGN部分的持有者数量（如：80）
#    - Axiom持有者数量 → Axiom部分的持有者数量（通常为null）
#    - 持有者数量 → 默认指向GMGN的持有者数量
#
# 2. 前10持有者占比（已区分）：
#    - GMGN前10持有者占比 → GMGN部分的前10持有者占比（如：27.5）
#    - 前10大持有者占比 → Axiom部分的前10大持有者占比（通常为null）
#    - 前10持有者占比 → 默认指向GMGN的前10持有者占比
#
# 3. Insiders（已区分）：
#    - "Insiders /老鼠仓" → Ave.ai标签统计部分（如：1）
#    - "Rug Pull Insiders" → Rug Pull统计部分（如：1.4）
#
# 4. Phishing（已区分）：
#    - "Phishing /钓鱼地址" → Ave.ai标签统计部分（如：0）
#    - "Rug Pull Phishing" → Rug Pull统计部分（如：0.0）
#
# 5. Cabal（已区分）：
#    - "Cabal /阴谋集团" → Ave.ai标签统计部分（如：0）
#    - "Rug Pull Cabal" → Rug Pull统计部分（如：0.0）
#
# 6. Bundle（已区分）：
#    - "Bundle /捆绑地址" → Ave.ai标签统计部分（如：0）
#    - "Rug Pull Bundle" → Rug Pull统计部分（如：0.0）
