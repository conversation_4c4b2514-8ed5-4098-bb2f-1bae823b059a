<!------------------------------------------------------------------------------------
**严格代码修改约束规则**

永远说中文 
在进行任何代码修改时，必须严格遵守以下约束：

1. **修改范围限制**：
   - 仅允许修改当前对话中明确提及和讨论的代码功能模块
   - 对于用户明确要求优化或修改的特定函数、类或代码段，才可以进行相应修改
   - 禁止修改任何未在当前对话中明确提及的代码部分
   - 确保方案简洁有效，不要过度工程化。

2. **业务逻辑保护**：
   - 所有现有的业务逻辑、算法核心逻辑必须保持完全不变
   - 不得改变函数的输入输出接口和行为预期
   - 不得修改配置参数、常量定义（除非用户明确要求）
   - 保持所有现有功能的完整性和正确性

3. **修改验证要求**：
   - 任何修改都必须基于用户在当前对话中的明确需求
   - 修改前必须明确说明将要修改的具体代码部分和修改原因
   - 如果不确定某个修改是否在用户要求范围内，必须先询问用户确认
   - 每一次终端执行命令都严格使用 max_wait_seconds=8（简单命令） 和 max_wait_seconds=16（复杂命令）。编辑修改代码的情况可以例外不用遵守此最长时间等待要求
   - 终端控制台的所有输出信息必须带时间戳

4. **禁止行为**：
   - 禁止"顺便"优化或修改未提及的代码
   - 禁止重构未要求修改的代码结构
   - 禁止添加用户未要求的新功能
   - 禁止删除或注释掉现有功能代码
   -绝对不允许使用模拟数据，和估算数据作为实测通过成功的理由
   -严禁擅自增加额外功能或优化
   -没让你改动的就不要改，哪怕是个小小的优化也不行，必须保持简洁优先
   -创建文件，查找文件等相关目录操作必须使用绝对完整路径。因为经常出现 
    相同的相对路径导致搞错文件夹目录的情况，浪费太多时间。
   -不要创建测试文件，所有修改直接在工作文件里进行然后进行直接实测，不要创建测试文件。
   -其他不相干代码一个字不要改

所有终端操作执行必须在当前的vscode终端里进行以便我能够看见所有输出
以后执行VSCode终端命令的最佳实践：
 使用单一命令：直接用 node -e "..."
 避免命令组合：不用 && 或 ;
 简化字符串：避免复杂的嵌套引号
 保持一致参数：始终用 wait=true
-----------
用户说"打开终端"、"启动程序"、"运行"时，使用 launch-process 工具
长期运行的程序设置 wait=false
短期命令设置 wait=true
用户说"读取"、"查看输出"时，才使用 read-terminal 工具
----------

不确定的参数或者设置可以上网搜索 ，或者告知我来处理
分析必须基于实际代码，不允许基于假设或推测。
不得进行如何形式的模拟测试，只能进行实际生产测试
pumpswap不是pumpfun（而是pumpfun完成迁移后的dex）使用的是pump-amm (或 market: "pumpSwap") ** = PumpSwap 上的标准 AMM 池，不要总是搞混淆。
当前是2025年7月

牢记，修改后先测试。测试成功后删除测试相关的所有临时文件。
请只专注于完善主程序功能，不要再创建额外的演示文件、说明文档或其他非必要文件。   

直接回答问题，减少不必要的工具调用
只在真正需要时才使用工具
避免冗余的状态检查
简洁明确的回复

----------------------------------------
 - 每一次终端执行命令都严格使用 max_wait_seconds=8（简单命令） 和 max_wait_seconds=17（复杂命令）。编辑修改代码的情况可以例外不用遵守此最长时间等待要求

- 每一次终端执行命令都严格使用只含英文的命令，尽量使用简短的英文命令。避免：长命令行，中文字符和复杂的字符串转义，否则容易报错

- 所有终端操作执行必须在当前的vscode终端里进行以便我能够看见所有输出

- gmgn api 文档链接：https://docs.gmgn.ai/cn/he-zuo-api-ji-cheng-gmgn-solana-jiao-yi-api（需要查看gmgn API 相關文檔的時候，直接訪問這個鏈接，不要自己上網去查。）

- 在没有明确发出修改代码的指令的情况下，不得修改添加删除任何代码，不得创建测试文件，脚本或者代码，

- 所有的分析，总结和说明必须必須基於實際代碼，而不是假設或者猜想。 

- 代码修改要保持简洁，避免过度工程化

- 解析不要有任何中文和emoji表情符号

- 永远使用文件完整路径，因为有时联合工作区有2个并列的文件夹里面存在相同文件名名称（比如main.py），使用相对路径会搞混淆。

- 任何时候测试时如果需要使用测试代币地址时，只能使用这个测试代币地址：HD3JBABeFkdZwUgKwhwJYqjLNrPWXEaDVfH4uMqRpump

- 你是一个非常擅长合并、汇总、集成代码模块和代码文件的高级程序代码工程师，代码程序员。 
Add Rules to this file or a short description and have Kiro refine them for you:   
-------------------------------------------------------------------------------------> 