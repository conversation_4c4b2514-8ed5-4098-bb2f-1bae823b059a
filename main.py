import asyncio
import websockets
import json
import requests
import time
import logging
import sys
import codecs
import re
import os
import unicodedata
from datetime import datetime
import telegram
# from telegram.request import HTTPXRequest  # 不同版本的导入路径不同，在函数内部处理

from telethon import TelegramClient
from telethon.errors import SessionPasswordNeededError
import concurrent.futures
from typing import Dict, List, Optional, Any
from concurrent.futures import ThreadPoolExecutor
from urllib.parse import urlencode

# GMGN API 依赖
try:
    import curl_cffi
    GMGN_AVAILABLE = True
    logger = logging.getLogger(__name__)  # 临时定义，稍后会被正式定义覆盖
    logger.info("✅ curl_cffi已导入，GMGN API功能可用")
except ImportError:
    GMGN_AVAILABLE = False
    logger = logging.getLogger(__name__)  # 临时定义，稍后会被正式定义覆盖
    logger.warning("❌ curl_cffi未安装，GMGN API功能不可用")

# 设置控制台输出编码为UTF-8，解决中文显示问题
if sys.stdout.encoding != 'utf-8':
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer, 'strict')
if sys.stderr.encoding != 'utf-8':
    sys.stderr = codecs.getwriter('utf-8')(sys.stderr.buffer, 'strict')

# Unicode字符清理函数
def clean_unicode_for_logging(text):
    """清理Unicode字符，移除代理对字符和其他问题字符"""
    if not isinstance(text, str):
        text = str(text)

    try:
        # 方法1：移除代理对字符
        cleaned = ''.join(char for char in text if not (0xD800 <= ord(char) <= 0xDFFF))

        # 方法2：标准化Unicode字符
        cleaned = unicodedata.normalize('NFKC', cleaned)

        # 方法3：移除控制字符（保留换行符和制表符）
        cleaned = ''.join(char for char in cleaned if unicodedata.category(char)[0] != 'C' or char in '\n\t\r')

        # 方法4：确保可以编码为UTF-8
        cleaned.encode('utf-8')

        return cleaned
    except Exception:
        # 如果所有方法都失败，使用错误处理编码
        return text.encode('utf-8', errors='ignore').decode('utf-8', errors='ignore')

# 自定义日志格式化器
class SafeFormatter(logging.Formatter):
    def format(self, record):
        # 清理日志消息中的Unicode字符
        if hasattr(record, 'msg') and isinstance(record.msg, str):
            record.msg = clean_unicode_for_logging(record.msg)

        # 清理参数中的Unicode字符
        if hasattr(record, 'args') and record.args:
            cleaned_args = []
            for arg in record.args:
                if isinstance(arg, str):
                    cleaned_args.append(clean_unicode_for_logging(arg))
                else:
                    cleaned_args.append(arg)
            record.args = tuple(cleaned_args)

        return super().format(record)

# 配置日志
safe_formatter = SafeFormatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')

# 创建处理器
console_handler = logging.StreamHandler()
console_handler.setFormatter(safe_formatter)

file_handler = logging.FileHandler("token_aggregator.log", encoding='utf-8')
file_handler.setFormatter(safe_formatter)

# 配置根日志器
logging.basicConfig(
    level=logging.INFO,
    handlers=[console_handler, file_handler]
)
logger = logging.getLogger(__name__)

# 电报配置
TELEGRAM_TOKEN = "**********************************************"
TELEGRAM_CHAT_ID = "-1002327446504"  # 群A - 接收所有消息
TELEGRAM_CHAT_ID_GROUP_B = "-1002178035321"  # 群B - 接收过滤后的消息（需要手动配置）

# 代理开关配置
USE_PROXY = True  # 设置为 True 启用代理，False 禁用代理
PROXY_URL = "http://127.0.0.1:7890"  # 代理服务器地址

# 根据开关设置代理配置
if USE_PROXY:
    PROXY = PROXY_URL
    PROXY_DICT = {
        'http': PROXY_URL,
        'https': PROXY_URL
    }
    logger.info(f"✅ 代理已启用: {PROXY_URL}")
else:
    PROXY = None
    PROXY_DICT = None
    logger.info("🔗 使用直连，代理已禁用")

# Telegram API 认证信息 (用于telegram_token_finder)
API_ID = 24702520
API_HASH = '7b1f880deb5998f36a079cfdbd097534'
PHONE_NUMBER = '+8615922595812'  # 用于首次认证

# 目标频道ID
TARGET_CHANNELS = [
    -1002030160217,  # Pump.fun • PumpSwap listing
    -1002049757467,  # Pump内盘Alert-猎人
    -1002021962831,  # Pump发射Alert-猎人
    -1002115686230   # Pump Alert - GMGN
]

# 频道类型映射
CHANNEL_TYPE_MAP = {
    -1002030160217: 'pumpfun',
    -1002049757467: 'hunter',
    -1002021962831: 'hunter',  # 新猎人频道
    -1002115686230: 'gmgn'
}

# 会话文件名 - 支持多实例运行但保持会话持久性
import os
import sys

def get_session_name():
    """获取会话文件名，支持多实例但保持会话持久性"""
    # 方案1：基于启动参数
    if len(sys.argv) > 1:
        instance_id = sys.argv[1]
        session_name = f"telegram_session_{instance_id}"
        logger.info(f"使用指定实例ID: {session_name}")
        return session_name

    # 方案2：基于端口号（如果有的话）
    try:
        # 检查是否有环境变量指定实例ID
        instance_id = os.environ.get('INSTANCE_ID')
        if instance_id:
            session_name = f"telegram_session_{instance_id}"
            logger.info(f"使用环境变量实例ID: {session_name}")
            return session_name
    except:
        pass

    # 方案3：默认使用固定名称（主实例）
    session_name = "telegram_search_session"
    logger.info(f"使用默认会话文件名: {session_name}")
    return session_name

SESSION_FILE = get_session_name()

# 功能1：过滤配置字段映射表（电报消息显示名 -> 代码字段名）
# 基于实际电报消息内容的完整映射
FIELD_MAPPING = {
    # === 基础信息字段 ===
    "代币地址": "token_address",
    "名称": "name",
    "符号": "symbol",
    "描述": "description",
    "创建者": "creator",
    "创建时间": "created_timestamp",

    # === SolanaTracker新增字段 ===
    "狙击数": "snipers_count",
    "狙击占比": "snipers_percentage",
    "内幕数": "insiders_count",
    "内幕占比": "insiders_percentage",
    "top 10占比": "top10_percentage",
    "holder人数": "holders_count",
    "1m Change": "price_change_1m",
    "5m Change": "price_change_5m",

    # === 时间相关字段 ===
    "创建时间分钟差": "created_time_diff_minutes",

    # === Axiom指标字段 ===
    "开发者代币总数": "axiom_totalCount",
    "已迁移代币数": "axiom_migratedCount",
    "Axiom持有者数量": "axiom_numHolders",  # Axiom部分的持有者数量
    "机器人用户数": "axiom_numBotUsers",
    "前10大持有者占比": "axiom_top10HoldersPercent",  # Axiom部分
    "开发者持有占比": "axiom_devHoldsPercent",
    "内部人士持有占比": "axiom_insidersHoldPercent",
    "打包者持有占比": "axiom_bundlersHoldPercent",
    "狙击手持有占比": "axiom_snipersHoldPercent",
    "总配对费用": "axiom_totalPairFeesPaid",

    # === Ave.ai标签统计字段 ===
    "Cabal /阴谋集团": "ave_cabal",  # Ave.ai标签统计部分
    "Phishing /钓鱼地址": "ave_phishing",  # Ave.ai标签统计部分
    "Insiders /老鼠仓": "ave_insiders",  # Ave.ai标签统计部分
    "Bundle /捆绑地址": "ave_bundle",  # Ave.ai标签统计部分
    "Snipers /狙击": "ave_snipers",
    "DEV /DEV": "ave_dev",
    "Whale /巨鲸": "ave_whale",
    "Smarters /聪明钱": "ave_smarters",
    "KOL /KOL": "ave_kol",
    "Rug Pull统计": "ave_rug_pull_stats",
    "Rug Pull Insiders": "ave_insiders_rate",  # Rug Pull统计下的Insiders
    "Rug Pull Phishing": "ave_phishing_rate",  # Rug Pull统计下的Phishing
    "Rug Pull Cabal": "ave_cabal_rate",  # Rug Pull统计下的Cabal
    "Rug Pull Bundle": "ave_bundle_rate",  # Rug Pull统计下的Bundle
    "All Tag Rate": "ave_all_tag_rate",

    # === GMGN统计数据字段 ===
    "GMGN持有者数量": "gmgn_holder_count",  # GMGN部分的持有者数量
    "持有者数量": "gmgn_holder_count",  # 默认指向GMGN的持有者数量（常用）
    "蓝筹持有者占比": "gmgn_bluechip_owner_percentage",
    "信号数量": "gmgn_signal_count",
    "Degen呼叫数": "gmgn_degen_call_count",
    "顶级老鼠交易者占比": "gmgn_top_rat_trader_percentage",
    "顶级打包者占比": "gmgn_top_bundler_trader_percentage",
    "顶级陷阱交易者占比": "gmgn_top_entrapment_trader_percentage",
    "平均持有余额": "gmgn_avg_holding_balance",
    "GMGN前10持有者占比": "gmgn_top10_holder_percent",  # GMGN部分
    "前10持有者占比": "gmgn_top10_holder_percent",  # 默认指向GMGN的前10持有者占比
    "前100持有者占比": "gmgn_top100_holder_percent",

    # === 钱包类型字段 ===
    "智能钱包": "gmgn_smart_wallets",
    "新钱包": "gmgn_fresh_wallets",
    "知名钱包": "gmgn_renowned_wallets",
    "创建者钱包": "gmgn_creator_wallets",
    "狙击手钱包": "gmgn_sniper_wallets",
    "老鼠交易者钱包": "gmgn_rat_trader_wallets",
    "鲸鱼钱包": "gmgn_whale_wallets",
    "顶级钱包": "gmgn_top_wallets",
    "关注钱包": "gmgn_following_wallets",
    "打包者钱包": "gmgn_bundler_wallets",

    # === 交易数据字段 ===
    "Twitter变更次数": "gmgn_twitter_change",
    "Twitter删除帖子代币数": "gmgn_twitter_del_post_token_count",
    "Twitter创建代币数": "gmgn_twitter_create_token_count",
    "创建者代币余额": "gmgn_creator_token_balance",
    "DEX广告状态": "gmgn_dexscr_ad",
    "CTO标志": "gmgn_cto_flag",
    "当前价格": "gmgn_price",
    "1分钟价格": "gmgn_price_1m",
    "1分钟买入次数": "gmgn_buys_1m",
    "1分钟卖出次数": "gmgn_sells_1m",
    "1分钟交易量": "gmgn_volume_1m",
    "1分钟买入量": "gmgn_buy_volume_1m",
    "1分钟卖出量": "gmgn_sell_volume_1m",
    "5分钟买入次数": "gmgn_buys_5m",
    "5分钟卖出次数": "gmgn_sells_5m",
    "5分钟交易量": "gmgn_volume_5m",
    "5分钟买入量": "gmgn_buy_volume_5m",
    "5分钟卖出量": "gmgn_sell_volume_5m",
    "热度等级": "gmgn_hot_level",

    # === 持有者行为数据字段 ===
    "hold": "gmgn_top_buyers_hold",
    "bought_more": "gmgn_top_buyers_bought_more",
    "transfered": "gmgn_top_buyers_transfered",
    "bought_rate": "gmgn_top_buyers_bought_rate",
    "holding_rate": "gmgn_top_buyers_holding_rate",
    "符合条件的钱包数量": "gmgn_filtered_holders_count",
    "最大持有者占比": "gmgn_biggest_holder",
    "native_balance >= 6000000000 的个数": "gmgn_native_balance_ge_6b",
    "tags 中包含 photon 的次数": "gmgn_tags_photon_count",
    "transfer_in: true 出现次数": "gmgn_transfer_in_true",
    "is_new: true 出现次数": "gmgn_is_new_true",
    "is_suspicious: true 出现次数": "gmgn_is_suspicious_true",
    "sandwich_bot 出现次数": "gmgn_sandwich_bot_count",
    "bundler 出现次数": "gmgn_bundler_count",

    # === 其他信息字段 ===
    "网站": "website",
    "Twitter": "twitter",
    "Telegram": "telegram",
    "DexPaid支付时间": "dexpaid_payment_time_diff_minutes",
    "跑路概率": "telegram_rug_probability",
    "猎人频道信息": "telegram_hunter_content",
    "相关消息数量": "telegram_message_count",
    "检测时间": "timestamp"
}

# 全局变量
bot = telegram.Bot(token=TELEGRAM_TOKEN)

# GMGN稳定性控制
PROGRAM_START_TIME = time.time()  # 程序启动时间
GMGN_SESSION_MAX_AGE = 24 * 3600  # 12小时后重建GMGN连接

# Telegram客户端 - 根据代理开关配置
if USE_PROXY:
    # 使用代理配置
    from telethon.network.connection.tcpfull import ConnectionTcpFull
    import socks
    telegram_client = TelegramClient(
        SESSION_FILE,
        API_ID,
        API_HASH,
        proxy=(socks.HTTP, '127.0.0.1', 7890)  # 使用代理
    )
else:
    # 直连配置（无代理）
    telegram_client = TelegramClient(
        SESSION_FILE,
        API_ID,
        API_HASH,
        proxy=None  # 不使用代理，直连
    )

class AxiomAPIClient:
    """Axiom API客户端，用于替换网页爬取"""

    def __init__(self):
        self.session = requests.Session()

        # 根据代理开关设置代理
        if USE_PROXY:
            self.session.proxies = PROXY_DICT
            logger.debug(f"AxiomAPIClient 使用代理: {PROXY_URL}")
        else:
            logger.debug("AxiomAPIClient 使用直连")

        # 使用与axiom_raw_data_display.py相同的完整认证headers
        cookie_string = "auth-access-token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhdXRoZW50aWNhdGVkVXNlcklkIjoiODBhN2JlMTctMWE5Zi00YTM2LWIwMDktODUxYzBkMDJkZDE2IiwiaWF0IjoxNzQ5Mzc0ODY3LCJleHAiOjE3NDkzNzU4Mjd9.51Tv7eLTwwdKFSIZtu7OP0Jv0MavciRg702JEBcPUaM; auth-refresh-token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyZWZyZXNoVG9rZW5JZCI6IjM3MDVlM2ZiLTUyMWQtNDU0ZS04ZGU2LWM4YWIzZjRmODUwYyIsImlhdCI6MTc0OTI4MjQzM30.hiWthz-WMxK8cZafQpVC_8iGDSSquIrgNYLOW8Rz0c0; g_state={\"i_l\":0}"

        self.session.headers.update({
            'accept': 'application/json, text/plain, */*',
            'accept-encoding': 'gzip, deflate, br, zstd',
            'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'cache-control': 'no-cache',
            'cookie': cookie_string,
            'origin': 'https://axiom.trade',
            'pragma': 'no-cache',
            'referer': 'https://axiom.trade/',
            'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-site',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        })

    def filter_dev_tokens_data(self, data):
        """筛选开发者代币列表数据"""
        filtered = {}
        if "counts" in data:
            counts = data["counts"]
            if "totalCount" in counts:
                filtered["totalCount"] = counts["totalCount"]
            if "migratedCount" in counts:
                filtered["migratedCount"] = counts["migratedCount"]
        return filtered

    def filter_token_info_data(self, data):
        """筛选代币信息数据"""
        filtered = {}
        required_fields = ["numHolders", "numBotUsers", "top10HoldersPercent", "devHoldsPercent",
                          "insidersHoldPercent", "bundlersHoldPercent", "snipersHoldPercent", "totalPairFeesPaid"]
        for field in required_fields:
            if field in data:
                value = data[field]
                # 对百分比字段保留一位小数
                if field.endswith('Percent') and isinstance(value, (int, float)):
                    filtered[field] = round(float(value), 1)
                # 对费用字段保留一位小数
                elif field == 'totalPairFeesPaid' and isinstance(value, (int, float)):
                    filtered[field] = round(float(value), 1)
                else:
                    filtered[field] = value
        return filtered

    def call_api(self, name, url, params=None, method="GET"):
        """调用单个API"""
        try:
            logger.info(f"调用{name} API: {url}")
            if method == "GET":
                response = self.session.get(url, params=params, timeout=6)
            else:
                response = self.session.post(url, json=params, timeout=6)

            if response.status_code == 200:
                data = response.json()
                logger.info(f"{name} API调用成功")
                return data
            else:
                logger.error(f"{name} API调用失败，状态码: {response.status_code}")
                return None
        except Exception as e:
            logger.error(f"{name} API调用出错: {e}")
            return None

    def get_axiom_data_concurrent(self, bonding_curve, creator):
        """并发获取Axiom相关数据"""
        try:
            # 使用bonding_curve作为pairAddress，creator作为devAddress
            apis = [
                {
                    "name": "开发者代币列表",
                    "url": "https://api.axiom.trade/dev-tokens-v2",
                    "params": {"devAddress": creator},
                    "filter_func": self.filter_dev_tokens_data
                },
                {
                    "name": "代币信息",
                    "url": "https://api.axiom.trade/token-info",
                    "params": {"pairAddress": bonding_curve},
                    "filter_func": self.filter_token_info_data
                }
            ]

            def call_single_api(api):
                """单个API调用函数"""
                try:
                    raw_data = self.call_api(
                        api["name"],
                        api["url"],
                        api.get("params"),
                        api.get("method", "GET")
                    )

                    if raw_data and "filter_func" in api:
                        filtered_data = api["filter_func"](raw_data)
                        return {"api": api["name"], "success": True, "data": filtered_data}

                    return {"api": api["name"], "success": False, "data": None}
                except Exception as e:
                    logger.error(f"{api['name']} API调用失败: {str(e)}")
                    return {"api": api["name"], "success": False, "error": str(e)}

            # 使用线程池并发执行
            with concurrent.futures.ThreadPoolExecutor(max_workers=len(apis)) as executor:
                # 提交所有任务
                future_to_api = {executor.submit(call_single_api, api): api for api in apis}

                # 等待所有任务完成
                results = {}
                for future in concurrent.futures.as_completed(future_to_api):
                    result = future.result()
                    if result["success"] and result["data"]:
                        # 将API数据合并到结果中
                        for key, value in result["data"].items():
                            results[f"axiom_{key}"] = value

            logger.info(f"Axiom API并发调用完成，获取到 {len(results)} 个字段")
            return results

        except Exception as e:
            logger.error(f"Axiom API并发调用出错: {e}")
            return {}

class AveApiClient:
    """Ave.ai API客户端，用于获取代币标签统计、风险评估和5分钟数据"""

    def __init__(self):
        """初始化客户端"""
        self.base_headers = {
            'accept': 'application/json, text/plain, */*',
            'accept-language': 'zh-CN,zh;q=0.9',
            'ave-udid': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36--1749210443316--bd7b897e-d6f8-4afe-97dd-28e3397768c7',
            'lang': 'en',
            'lang-zone': 'en',
            'origin': 'https://ave.ai',
            'referer': 'https://ave.ai/',
            'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'cross-site',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'x-auth': 'c72c2b2a7643c87ed5b8668308e160e61749210447850612429'
        }

    def call_api(self, url: str, description: str = '') -> Optional[Dict[str, Any]]:
        """通用API调用方法"""
        try:
            logger.info(f"调用Ave.ai接口: {description or url}")
            # 根据代理开关设置代理
            proxies = PROXY_DICT if USE_PROXY else None
            if USE_PROXY:
                logger.debug(f"Ave.ai API 使用代理: {PROXY_URL}")
            response = requests.get(url, headers=self.base_headers, proxies=proxies, timeout=10)
            response.raise_for_status()

            if not response.text.strip():
                logger.warning("Ave.ai响应内容为空")
                return None

            data = response.json()
            logger.info(f"Ave.ai API调用成功，状态: {response.status_code}")
            return data

        except requests.exceptions.RequestException as e:
            logger.error(f"Ave.ai请求错误: {e}")
            return None
        except json.JSONDecodeError as e:
            logger.error(f"Ave.ai JSON解析错误: {e}")
            return None
        except Exception as e:
            logger.error(f"Ave.ai未知错误: {e}")
            return None

    def get_tag_statistics(self, token_id: str) -> Optional[Dict[str, Any]]:
        """获取标签统计数据"""
        url = f"https://api.agacve.com/v1api/v3/stats/alltags/totalholders?token_id={token_id}"
        return self.call_api(url, "标签统计数据")

    def get_risk_assessment(self, token_id: str) -> Optional[Dict[str, Any]]:
        """获取风险评估数据"""
        url = f"https://api.agacve.com/v1api/v3/stats/rugpullrate?token_id={token_id}"
        return self.call_api(url, "风险评估数据")

    def get_token_details(self, token_id: str) -> Optional[Dict[str, Any]]:
        """获取Token详细信息（包含5分钟数据和聪明钱活动）"""
        url = f"https://api.agacve.com/v1api/v3/tokens/{token_id}"
        return self.call_api(url, "Token详细信息（5分钟数据）")

    def extract_tag_data(self, data: Dict[str, Any]) -> Dict[str, int]:
        """提取标签统计数据"""
        result = {}
        if not data or data.get('status') != 1:
            return result

        for item in data.get('data', []):
            tag_name = item.get('en', '').lower()
            total_address = item.get('total_address', 0)

            # 映射到指定的字段名
            if 'cabal' in tag_name:
                result['ave_cabal'] = total_address
            elif 'phishing' in tag_name:
                result['ave_phishing'] = total_address
            elif 'insider' in tag_name:
                result['ave_insiders'] = total_address
            elif 'bundle' in tag_name:
                result['ave_bundle'] = total_address

            elif 'sniper' in tag_name:
                result['ave_snipers'] = total_address
            elif 'dev' in tag_name:
                result['ave_dev'] = total_address
            elif 'whale' in tag_name:
                result['ave_whale'] = total_address
            elif 'smarter' in tag_name:
                result['ave_smarters'] = total_address
            elif 'kol' in tag_name:
                result['ave_kol'] = total_address

        return result

    def extract_risk_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """提取风险评估数据"""
        result = {}
        if not data or data.get('status') != 1:
            return result

        rates_data = data.get('data', {}).get('rates', {})

        # 基础统计
        rugged = rates_data.get('rugged', 0)
        rugged_rate = rates_data.get('rugged_rate', 0)
        total = rates_data.get('total', 0)

        result['ave_rug_pull_stats'] = f"{rugged}/{total} /{rugged_rate:.1f}/"

        # 风险比率详情
        for rate_item in rates_data.get('rateList', []):
            title = rate_item.get('title', {}).get('En', '').lower()
            rate = rate_item.get('rate', 0)

            if 'insider' in title:
                result['ave_insiders_rate'] = f"{rate:.1f}"
            elif 'phishing' in title:
                result['ave_phishing_rate'] = f"{rate:.1f}"
            elif 'cabal' in title:
                result['ave_cabal_rate'] = f"{rate:.1f}"
            elif 'bundle' in title:
                result['ave_bundle_rate'] = f"{rate:.1f}"
            elif 'all tag' in title:
                result['ave_all_tag_rate'] = f"{rate:.1f}"

        return result

    def extract_5minute_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """提取5分钟数据"""
        result = {}
        if not data or data.get('status') != 1:
            return result

        pairs = data.get('data', {}).get('pairs', [])
        if not pairs:
            return result

        # 取第一个交易对的数据
        pair_data = pairs[0]

        result['ave_price_change_5m'] = f"{pair_data.get('price_change_5m', 0):.1f}"
        result['ave_buy_volume_5m'] = f"{pair_data.get('buy_volume_u_5m', 0):.1f}"
        result['ave_sell_volume_5m'] = f"{pair_data.get('sell_volume_u_5m', 0):.1f}"
        result['ave_total_volume_5m'] = f"{pair_data.get('volume_u_5m', 0):.1f}"
        result['ave_buy_tx_5m'] = pair_data.get('buys_tx_5m_count', 0)
        result['ave_sell_tx_5m'] = pair_data.get('sells_tx_5m_count', 0)
        result['ave_buyers_5m'] = pair_data.get('buyers_5m', 0)
        result['ave_sellers_5m'] = pair_data.get('sellers_5m', 0)

        return result

    def extract_smart_money_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """提取聪明钱活动数据"""
        result = {}
        if not data or data.get('status') != 1:
            return result

        pairs = data.get('data', {}).get('pairs', [])
        if not pairs:
            return result

        pair_data = pairs[0]

        # 24小时聪明钱统计
       # result['ave_smart_buy_24h'] = pair_data.get('smart_money_buy_count_24h', 0)
       # result['ave_smart_sell_24h'] = pair_data.get('smart_money_sell_count_24h', 0)

       # return result

    def get_ave_data_concurrent(self, token_address: str) -> Dict[str, Any]:
        """并发获取Ave.ai相关数据"""
        try:
            # 构造token_id（Ave.ai格式）
            token_id = f"{token_address}-solana"

            logger.info(f"使用token_id获取Ave.ai数据: {token_id}")

            def call_single_api(api_func, *args):
                """单个API调用函数"""
                try:
                    return api_func(*args)
                except Exception as e:
                    logger.error(f"Ave.ai API调用失败: {str(e)}")
                    return None

            # 使用线程池并发执行
            with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
                # 提交所有任务
                future_tag = executor.submit(call_single_api, self.get_tag_statistics, token_id)
                future_risk = executor.submit(call_single_api, self.get_risk_assessment, token_id)
                future_token = executor.submit(call_single_api, self.get_token_details, token_id)

                # 等待所有任务完成
                tag_data = future_tag.result()
                risk_data = future_risk.result()
                token_data = future_token.result()

            # 提取数据
            results = {}

            if tag_data:
                tag_results = self.extract_tag_data(tag_data)
                results.update(tag_results)

            if risk_data:
                risk_results = self.extract_risk_data(risk_data)
                results.update(risk_results)

            if token_data:
                minute_results = self.extract_5minute_data(token_data)
                smart_results = self.extract_smart_money_data(token_data)
                results.update(minute_results)
                results.update(smart_results)

            logger.info(f"Ave.ai API并发调用完成，获取到 {len(results)} 个字段")
            return results

        except Exception as e:
            logger.error(f"Ave.ai API并发调用出错: {e}")
            return {}

class GmgnApiClient:
    """GMGN API客户端，用于获取代币统计数据 - 连接池轮换机制"""

    def __init__(self):
        """初始化客户端 - 连接池轮换版本"""
        if not GMGN_AVAILABLE:
            logger.warning("GMGN API客户端初始化失败：curl_cffi未安装")
            return

        # JSON文件保存开关 (集成到main.py时设为False)
        self.save_json_files = False

        # 连接池配置
        self.pool_size = 3  # 3个连接池
        self.session_pools = []  # 会话池列表
        self.pool_health = []  # 池健康状态
        self.pool_created_time = []  # 池创建时间
        self.current_pool_index = 0  # 当前使用的池索引

        # 轮换配置
        self.rotation_interval = 6 * 3600  # 2小时轮换一次
        self.last_rotation_time = time.time()
        self.max_pool_age = 12 * 3600  # 单个池最大存活时间10小时

        # 健康检查配置
        self.consecutive_failures = {}  # 每个池的连续失败次数
        self.max_failures_before_rebuild = 3  # 连续失败3次后重建池

        # 初始化连接池（错开时间）
        self._init_session_pools()

        logger.info(f"✅ GMGN连接池轮换机制已启动，池大小: {self.pool_size}")

    def _init_session_pools(self):
        """初始化会话池 - 错开创建时间"""
        logger.info("🔄 初始化GMGN连接池...")

        for i in range(self.pool_size):
            try:
                # 创建新会话
                session = self._create_fresh_session()
                current_time = time.time()

                self.session_pools.append(session)
                self.pool_health.append(True)
                self.pool_created_time.append(current_time)
                self.consecutive_failures[i] = 0

                logger.info(f"✅ 连接池 {i+1}/{self.pool_size} 初始化完成")

                # 错开创建时间，避免同时失效（除了第一个池）
                if i < self.pool_size - 1:
                    stagger_delay = 30  # 30秒间隔
                    logger.debug(f"⏳ 等待 {stagger_delay} 秒后创建下一个池...")
                    time.sleep(stagger_delay)

            except Exception as e:
                logger.error(f"❌ 连接池 {i+1} 初始化失败: {e}")
                self.session_pools.append(None)
                self.pool_health.append(False)
                self.pool_created_time.append(time.time())
                self.consecutive_failures[i] = 999  # 标记为不可用

    def _create_fresh_session(self):
        """创建新的curl_cffi会话"""
        if not GMGN_AVAILABLE:
            return None

        logger.debug("🔐 创建新的curl_cffi会话...")
        try:
            # 根据代理开关设置代理
            if USE_PROXY:
                session = curl_cffi.Session(
                    impersonate='chrome131',
                    timeout=30,
                    proxies=PROXY_DICT
                )
                logger.debug(f"GMGN API 使用代理: {PROXY_URL}")
            else:
                session = curl_cffi.Session(
                    impersonate='chrome131',
                    timeout=30
                )
                logger.debug("GMGN API 使用直连")
            return session
        except Exception as e:
            logger.error(f"创建会话失败: {e}")
            return None

    def get_healthy_session(self):
        """获取健康的会话 - 核心方法"""
        current_time = time.time()

        # 1. 检查是否需要预防性轮换
        if current_time - self.last_rotation_time > self.rotation_interval:
            self._proactive_rotation()

        # 2. 检查当前池是否健康且未过期
        if (self.pool_health[self.current_pool_index] and
            self.session_pools[self.current_pool_index] is not None and
            current_time - self.pool_created_time[self.current_pool_index] < self.max_pool_age):
            return self.session_pools[self.current_pool_index]

        # 3. 当前池不健康或过期，寻找健康的池
        healthy_pool_index = self._find_healthy_pool()
        if healthy_pool_index is not None:
            old_index = self.current_pool_index
            self.current_pool_index = healthy_pool_index
            logger.info(f"🔄 从连接池 {old_index+1} 切换到 {healthy_pool_index+1}")
            return self.session_pools[healthy_pool_index]

        # 4. 所有池都不健康，紧急重建当前池
        logger.warning("⚠️ 所有连接池都不健康，紧急重建当前池")
        self._emergency_rebuild_current_pool()
        return self.session_pools[self.current_pool_index]

    def _find_healthy_pool(self):
        """寻找健康的连接池"""
        current_time = time.time()

        for i in range(self.pool_size):
            if (self.pool_health[i] and
                self.session_pools[i] is not None and
                current_time - self.pool_created_time[i] < self.max_pool_age):
                return i
        return None

    def _proactive_rotation(self):
        """预防性轮换连接池"""
        logger.info("🔄 开始预防性连接池轮换")

        # 选择下一个池进行重建
        next_pool_index = (self.current_pool_index + 1) % self.pool_size

        # 在后台重建下一个池
        self._rebuild_pool(next_pool_index)

        # 切换到重建的池
        if self.pool_health[next_pool_index]:
            old_index = self.current_pool_index
            self.current_pool_index = next_pool_index
            self.last_rotation_time = time.time()
            logger.info(f"✅ 预防性轮换：从池 {old_index+1} 切换到池 {next_pool_index+1}")
        else:
            logger.warning(f"⚠️ 池 {next_pool_index+1} 重建失败，继续使用当前池")

    def _rebuild_pool(self, pool_index):
        """重建指定的连接池"""
        logger.info(f"🔄 重建连接池 {pool_index+1}")

        try:
            # 关闭旧会话
            if self.session_pools[pool_index] is not None:
                try:
                    self.session_pools[pool_index].close()
                except:
                    pass

            # 创建新会话
            new_session = self._create_fresh_session()
            if new_session is not None:
                self.session_pools[pool_index] = new_session
                self.pool_health[pool_index] = True
                self.pool_created_time[pool_index] = time.time()
                self.consecutive_failures[pool_index] = 0
                logger.info(f"✅ 连接池 {pool_index+1} 重建成功")
            else:
                self.pool_health[pool_index] = False
                logger.error(f"❌ 连接池 {pool_index+1} 重建失败")

        except Exception as e:
            logger.error(f"❌ 重建连接池 {pool_index+1} 时出错: {e}")
            self.pool_health[pool_index] = False

    def _emergency_rebuild_current_pool(self):
        """紧急重建当前连接池"""
        logger.warning(f"🚨 紧急重建当前连接池 {self.current_pool_index+1}")
        self._rebuild_pool(self.current_pool_index)

    def _mark_pool_failure(self, pool_index):
        """标记连接池失败"""
        self.consecutive_failures[pool_index] += 1
        logger.warning(f"⚠️ 连接池 {pool_index+1} 失败次数: {self.consecutive_failures[pool_index]}")

        if self.consecutive_failures[pool_index] >= self.max_failures_before_rebuild:
            logger.warning(f"❌ 连接池 {pool_index+1} 连续失败 {self.consecutive_failures[pool_index]} 次，标记为不健康")
            self.pool_health[pool_index] = False

    def _mark_pool_success(self, pool_index):
        """标记连接池成功"""
        if self.consecutive_failures[pool_index] > 0:
            logger.info(f"✅ 连接池 {pool_index+1} 恢复正常")
        self.consecutive_failures[pool_index] = 0

    def create_session(self):
        """兼容性方法 - 返回健康的会话"""
        return self.get_healthy_session()

    def get_common_params(self):
        """获取通用参数"""
        return {
            'device_id': '6a36ecc5-3fcf-45f5-8140-5f206318be93',
            'client_id': 'gmgn_web_20250610-2042-51c73c5',
            'from_app': 'gmgn',
            'app_ver': '20250610-2042-51c73c5',
            'tz_name': 'Atlantic/Reykjavik',
            'tz_offset': '0',
            'app_lang': 'en-US',
            'fp_did': '2d965d542b41282edc7d553d2cc4111a',
            'os': 'web'
        }

    def get_common_headers(self, token_address):
        """获取通用请求头 - 修复版本，包含所有必需的Chrome安全头"""
        return {
            # 核心浏览器标识
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',

            # 接受类型
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br, zstd',

            # 来源信息
            'Referer': f'https://gmgn.ai/sol/token/5Lo30dss_{token_address}',
            'Origin': 'https://gmgn.ai',

            # Chrome 安全头 - 完整版本
            'Sec-Ch-Ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
            'Sec-Ch-Ua-Arch': '"x86"',
            'Sec-Ch-Ua-Bitness': '"64"',
            'Sec-Ch-Ua-Full-Version': '"137.0.7151.69"',
            'Sec-Ch-Ua-Full-Version-List': '"Google Chrome";v="137.0.7151.69", "Chromium";v="137.0.7151.69", "Not/A)Brand";v="********"',
            'Sec-Ch-Ua-Mobile': '?0',
            'Sec-Ch-Ua-Model': '""',
            'Sec-Ch-Ua-Platform': '"Windows"',
            'Sec-Ch-Ua-Platform-Version': '"8.0.0"',

            # 请求类型
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',

            # Sentry 追踪
            'Sentry-Trace': '2fc0870e6cab4e55b9febe7a06a133d0-a18ceba5a04e9a1d-0',

            # Baggage 信息
            'Baggage': 'sentry-environment=production,sentry-release=20250610-2042-51c73c5,sentry-public_key=93c25bab7246077dc3eb85b59d6e7d40,sentry-trace_id=dd3cc18fedbb49e785864047107de8dc,sentry-sample_rate=0.005,sentry-sampled=false',

            # 连接控制
            'Connection': 'keep-alive',
            'Priority': 'u=1, i',

            # 关键 Cookies
            'Cookie': '_ga=GA1.1.599615528.1749086863; GMGN_CHAIN=sol; GMGN_THEME=dark; GMGN_LOCALE=en-US; sid=gmgn%7C9ebde571e10c5b0315cef92bb3530288; _ga_UGLVBMV4Z0=GS1.2.1749565523848833.af8cf921e0fdce73b52b5bc56b387bfa.2EcPLA8mFAkjxoOmT%2BDwXQ%3D%3D.Wc0rrOhXkUjkPq7l6ePu9Q%3D%3D.T0S7zHUc8do5KAnJ0vFQ3Q%3D%3D.LIt9kg%2Figmx8pUqDBlAbVw%3D%3D; __cf_bm=xRGF447nHoudrlbWdq2P_cky89BW.f5C7QOuBxKanMA-1749566338-1.0.1.1-pdjVH0w4dzZ02ZAjqXtftouSEVhLOPW79poiWxHr86hovgfPMEidV3HENExium_5sNvypxwo0Nc_39AIwoBAcZPEP5UOuDz_p4C5oHdK4Iw; cf_clearance=cXCYlAMDnXOdto8gArSF5R_s1A9Gs_6gCbO8tqi7yKI-1749566592-1.2.1.1-p5qQL7OKRGd6paPbWnLz1noo99ouZEF0Gu2VC1ia.HtyXls5UP4Ra2VQy__Glf6CP_t2zXQcEgCOUABPa3lZBOAflIxh1TehvrIxcW8bBA7INW08e23I3_kmZfl6ofPwF.ZdF88PJsWlPaug30E2HFgOIYTNcC7DOYELbayY76XGvWh3eVUMBx16e4Clhcj030SoMhIjpXA2v3CaanWsInQbRrPcSTRKbzpxhjiqO_B68eOW8_z0.ul1GB2tXe.sGZaCV310tNAjm2GEA_pj3IyRlYfYa2py86ZoEVai5.j49Mk4hObKK5Y4N56eawazXV7jV_nEOZ_KcD2CNa8b9EDuMv4aQCNaanQ8A8dvg2U; _ga_0XM0LYXGC8=GS2.1.s1749537359$o18$g1$t1749566601$j6$l0$h0'
        }

    def call_gmgn_api(self, api_name, url, params=None, token_address=None):
        """通用GMGN API调用函数 - 连接池版本"""
        if not GMGN_AVAILABLE:
            logger.warning(f"GMGN API不可用，跳过{api_name}")
            return None

        max_retries = 2  # 最多重试2次（使用不同的池）

        for attempt in range(max_retries + 1):
            try:
                # 获取健康的会话
                session = self.get_healthy_session()
                if not session:
                    logger.error(f"❌ 无法获取健康的会话用于 {api_name}")
                    if attempt < max_retries:
                        continue
                    return None

                current_pool = self.current_pool_index
                logger.debug(f"🎯 调用 {api_name} (池 {current_pool+1}, 尝试 {attempt+1})")

                # 设置请求头
                headers = self.get_common_headers(token_address)
                session.headers.update(headers)

                # 合并参数
                if params is None:
                    params = {}
                common_params = self.get_common_params()
                final_params = {**common_params, **params}

                # 发送请求
                response = session.get(url, params=final_params)

                if response.status_code == 200:
                    try:
                        data = response.json()
                        # 检查返回数据是否有效
                        if data and data != 'null':
                            logger.debug(f"✅ {api_name} 调用成功! (池 {current_pool+1})")
                            self._mark_pool_success(current_pool)
                            return data
                        else:
                            logger.warning(f"⚠️ {api_name} 返回空数据 (池 {current_pool+1})")
                            self._mark_pool_failure(current_pool)
                            if attempt < max_retries:
                                continue
                            return None
                    except json.JSONDecodeError as e:
                        logger.error(f"❌ {api_name} JSON解析失败: {e} (池 {current_pool+1})")
                        self._mark_pool_failure(current_pool)
                        if attempt < max_retries:
                            continue
                        return None
                else:
                    logger.error(f"❌ {api_name} 调用失败: {response.status_code} (池 {current_pool+1})")
                    self._mark_pool_failure(current_pool)
                    if attempt < max_retries:
                        continue
                    return None

            except Exception as e:
                current_pool = self.current_pool_index
                logger.error(f"❌ {api_name} 调用异常: {e} (池 {current_pool+1})")
                self._mark_pool_failure(current_pool)
                if attempt < max_retries:
                    # 等待一小段时间后重试
                    time.sleep(1)
                    continue
                return None

        logger.error(f"❌ {api_name} 所有重试都失败")
        return None

    def call_gmgn_mutil_window_api(self, token_address):
        """调用GMGN mutil_window_token_info API - 1-24 buy sell - 连接池版本"""
        if not GMGN_AVAILABLE:
            logger.warning("GMGN API不可用，跳过Multi Window API")
            return None

        max_retries = 2
        api_name = "Multi Window API"

        for attempt in range(max_retries + 1):
            try:
                # 获取健康的会话
                session = self.get_healthy_session()
                if not session:
                    logger.error(f"❌ 无法获取健康的会话用于 {api_name}")
                    if attempt < max_retries:
                        continue
                    return None

                current_pool = self.current_pool_index
                logger.debug(f"🎯 调用 {api_name} (池 {current_pool+1}, 尝试 {attempt+1})")

                # 构建完整的URL
                base_url = "https://gmgn.ai/api/v1/mutil_window_token_info"
                url_params = self.get_common_params()
                full_url = f"{base_url}?{urlencode(url_params)}"

                # 设置请求头
                headers = self.get_common_headers(token_address)
                session.headers.update(headers)

                # POST请求体数据
                post_data = {
                    "chain": "sol",
                    "addresses": [token_address]
                }

                # 发送POST请求
                response = session.post(full_url, json=post_data)

                if response.status_code == 200:
                    try:
                        data = response.json()
                        if data and data != 'null':
                            logger.debug(f"✅ {api_name} 调用成功! (池 {current_pool+1})")
                            self._mark_pool_success(current_pool)
                            return data
                        else:
                            logger.warning(f"⚠️ {api_name} 返回空数据 (池 {current_pool+1})")
                            self._mark_pool_failure(current_pool)
                            if attempt < max_retries:
                                continue
                            return None
                    except json.JSONDecodeError as e:
                        logger.error(f"❌ {api_name} JSON解析失败: {e} (池 {current_pool+1})")
                        self._mark_pool_failure(current_pool)
                        if attempt < max_retries:
                            continue
                        return None
                else:
                    logger.error(f"❌ {api_name} 调用失败: {response.status_code} (池 {current_pool+1})")
                    self._mark_pool_failure(current_pool)
                    if attempt < max_retries:
                        continue
                    return None

            except Exception as e:
                current_pool = self.current_pool_index
                logger.error(f"❌ {api_name} 调用异常: {e} (池 {current_pool+1})")
                self._mark_pool_failure(current_pool)
                if attempt < max_retries:
                    time.sleep(1)
                    continue
                return None

        logger.error(f"❌ {api_name} 所有重试都失败")
        return None

    def call_gmgn_token_stat_api(self, token_address):
        """调用GMGN token_stat API"""
        api_url = f"https://gmgn.ai/api/v1/token_stat/sol/{token_address}"
        return self.call_gmgn_api("Token Stat API", api_url, token_address=token_address)

    def call_gmgn_token_trends_api(self, token_address):
        """调用GMGN token_trends API - 平均持有"""
        api_url = f"https://gmgn.ai/api/v1/token_trends/sol/{token_address}"
        params = {
            'trends_type': ['avg_holding_balance', 'holder_count', 'top10_holder_percent', 'top100_holder_percent']
        }
        return self.call_gmgn_api("Token Trends API (平均持有)", api_url, params, token_address)

    def call_gmgn_wallet_tags_api(self, token_address):
        """调用GMGN wallet_tags_stat API - 钱包地址"""
        api_url = f"https://gmgn.ai/api/v1/token_wallet_tags_stat/sol/{token_address}"
        return self.call_gmgn_api("Wallet Tags API (钱包地址)", api_url, token_address=token_address)

    def call_gmgn_top_buyers_api(self, token_address):
        """调用GMGN top_buyers API - sniper 70"""
        api_url = f"https://gmgn.ai/defi/quotation/v1/tokens/top_buyers/sol/{token_address}"
        return self.call_gmgn_api("Top Buyers API (sniper 70)", api_url, token_address=token_address)

    def call_gmgn_token_holders_api(self, token_address):
        """调用GMGN token_holders API - top100"""
        api_url = f"https://gmgn.ai/vas/api/v1/token_holders/sol/{token_address}"
        params = {
            'limit': '100',
            'cost': '20',
            'orderby': 'amount_percentage',
            'direction': 'desc'
        }
        return self.call_gmgn_api("Token Holders API (top100)", api_url, params, token_address)

    def extract_gmgn_data(self, all_api_data, token_address):
        """从所有GMGN API响应中提取关键数据"""
        try:
            logger.debug("🔍 开始提取GMGN数据...")

            result = {}

            # 提取token_stat数据 - 完整版本
            if 'token_stat' in all_api_data and all_api_data['token_stat']:
                token_stat = all_api_data['token_stat'].get('data', {})

                # 输出详细的TOKEN_STAT字段展示
                if token_stat:
                    logger.info(f"📊 TOKEN_STAT 数据展示:")

                    count = 0
                    # 定义需要过滤的字段（避免重复显示）
                    filtered_fields = {'bluechip_owner_count'}

                    for key, value in token_stat.items():
                        # 过滤掉指定字段
                        if key in filtered_fields:
                            continue
                        if count >= 5:  # 显示前5个字段
                            break

                        # 处理不同类型的值
                        if value is None:
                            display_value = "null"
                        elif key in ['bluechip_owner_percentage', 'top_rat_trader_percentage']:
                            # 特殊字段直接进入数字处理逻辑
                            pass  # 跳过其他条件，直接进入数字处理
                        elif isinstance(value, str):
                            if len(value) > 50:
                                display_value = value[:50] + "..."
                            elif value == "":
                                display_value = "null"
                            else:
                                display_value = value
                        elif isinstance(value, (list, dict)):
                            if len(value) == 0:
                                display_value = "null"
                            else:
                                display_value = f"{type(value).__name__}(长度: {len(value)})"
                        elif value == 0 and key in ['price', 'market_cap', 'volume_24h']:
                            display_value = "null"  # 价格相关字段为0时显示null
                        else:
                            # 其他情况的数字处理
                            pass

                        # 数字处理逻辑（包括特殊字段和其他数字类型）
                        if key in ['bluechip_owner_percentage', 'top_rat_trader_percentage'] or (value is not None and not isinstance(value, str) and not isinstance(value, (list, dict)) and not (value == 0 and key in ['price', 'market_cap', 'volume_24h'])):
                            # 特殊处理需要乘以100的字段
                            # 先尝试转换为数字
                            try:
                                if isinstance(value, str):
                                    numeric_value = float(value)
                                else:
                                    numeric_value = value
                            except (ValueError, TypeError):
                                numeric_value = value

                            if key in ['bluechip_owner_percentage', 'top_rat_trader_percentage']:
                                # 重新转换，确保使用正确的值
                                try:
                                    if isinstance(value, str):
                                        field_numeric_value = float(value)
                                    else:
                                        field_numeric_value = value
                                except (ValueError, TypeError):
                                    field_numeric_value = value

                                if isinstance(field_numeric_value, (int, float)):
                                    display_value = f"{field_numeric_value * 100:.1f}"
                                else:
                                    display_value = value
                            else:
                                display_value = value

                        logger.info(f"  ✅ {key}: {display_value}")
                        count += 1

                    # 显示剩余字段数量
                    remaining_count = len([k for k in token_stat.keys() if k not in filtered_fields]) - count
                    if remaining_count > 0:
                        logger.info(f"  ... 还有 {remaining_count} 个字段")

                # 提取数据到结果中（应用格式化处理）
                result['gmgn_holder_count'] = token_stat.get('holder_count', 'null')

                # 特殊处理需要乘以100保留1位小数的字段
                bluechip_percentage = token_stat.get('bluechip_owner_percentage', 'null')
                if bluechip_percentage != 'null':
                    try:
                        if isinstance(bluechip_percentage, str):
                            numeric_value = float(bluechip_percentage)
                        else:
                            numeric_value = bluechip_percentage

                        if isinstance(numeric_value, (int, float)):
                            result['gmgn_bluechip_owner_percentage'] = f"{numeric_value * 100:.1f}"
                        else:
                            result['gmgn_bluechip_owner_percentage'] = bluechip_percentage
                    except (ValueError, TypeError):
                        result['gmgn_bluechip_owner_percentage'] = bluechip_percentage
                else:
                    result['gmgn_bluechip_owner_percentage'] = bluechip_percentage

                result['gmgn_signal_count'] = token_stat.get('signal_count', 'null')
                result['gmgn_degen_call_count'] = token_stat.get('degen_call_count', 'null')

                # 特殊处理需要乘以100保留1位小数的字段
                top_rat_percentage = token_stat.get('top_rat_trader_percentage', 'null')
                if top_rat_percentage != 'null':
                    try:
                        if isinstance(top_rat_percentage, str):
                            numeric_value = float(top_rat_percentage)
                        else:
                            numeric_value = top_rat_percentage

                        if isinstance(numeric_value, (int, float)):
                            result['gmgn_top_rat_trader_percentage'] = f"{numeric_value * 100:.1f}"
                        else:
                            result['gmgn_top_rat_trader_percentage'] = top_rat_percentage
                    except (ValueError, TypeError):
                        result['gmgn_top_rat_trader_percentage'] = top_rat_percentage
                else:
                    result['gmgn_top_rat_trader_percentage'] = top_rat_percentage

                # 特殊处理需要乘以100保留1位小数的字段
                top_bundler_percentage = token_stat.get('top_bundler_trader_percentage', 'null')
                if top_bundler_percentage != 'null':
                    try:
                        if isinstance(top_bundler_percentage, str):
                            numeric_value = float(top_bundler_percentage)
                        else:
                            numeric_value = top_bundler_percentage

                        if isinstance(numeric_value, (int, float)):
                            result['gmgn_top_bundler_trader_percentage'] = f"{numeric_value * 100:.1f}"
                        else:
                            result['gmgn_top_bundler_trader_percentage'] = top_bundler_percentage
                    except (ValueError, TypeError):
                        result['gmgn_top_bundler_trader_percentage'] = top_bundler_percentage
                else:
                    result['gmgn_top_bundler_trader_percentage'] = top_bundler_percentage

                # 特殊处理需要乘以100保留1位小数的字段
                top_entrapment_percentage = token_stat.get('top_entrapment_trader_percentage', 'null')
                if top_entrapment_percentage != 'null':
                    try:
                        if isinstance(top_entrapment_percentage, str):
                            numeric_value = float(top_entrapment_percentage)
                        else:
                            numeric_value = top_entrapment_percentage

                        if isinstance(numeric_value, (int, float)):
                            result['gmgn_top_entrapment_trader_percentage'] = f"{numeric_value * 100:.1f}"
                        else:
                            result['gmgn_top_entrapment_trader_percentage'] = top_entrapment_percentage
                    except (ValueError, TypeError):
                        result['gmgn_top_entrapment_trader_percentage'] = top_entrapment_percentage
                else:
                    result['gmgn_top_entrapment_trader_percentage'] = top_entrapment_percentage

            # 提取token_trends数据（最新值）- 完整版本
            if 'token_trends' in all_api_data and all_api_data['token_trends']:
                trends_data = all_api_data['token_trends'].get('data', {}).get('trends', {})

                # 输出详细的趋势数据
                if trends_data:
                    logger.info(f"📊 趋势数据 (最新值):")

                    for trend_type, trend_list in trends_data.items():
                        # 过滤掉 holder_count 字段（避免重复显示）
                        if trend_type == 'holder_count':
                            continue

                        if isinstance(trend_list, list) and len(trend_list) > 0:
                            # 获取最新的值 (数组最后一个元素)
                            latest_data = trend_list[-1]
                            if isinstance(latest_data, dict) and 'value' in latest_data:
                                latest_value = latest_data['value']
                                timestamp = latest_data.get('timestamp', 'unknown')
                                # 特殊处理需要乘以100的字段
                                # 先尝试转换为数字
                                try:
                                    if isinstance(latest_value, str):
                                        numeric_value = float(latest_value)
                                    else:
                                        numeric_value = latest_value
                                except (ValueError, TypeError):
                                    numeric_value = latest_value

                                if trend_type in ['top100_holder_percent', 'top10_holder_percent', 'bluechip_owner_percentage'] and isinstance(numeric_value, (int, float)):
                                    formatted_value = f"{numeric_value * 100:.1f}"
                                # 特殊处理需要去除小数点的字段
                                elif trend_type == 'avg_holding_balance' and isinstance(numeric_value, (int, float)):
                                    formatted_value = f"{int(numeric_value)}"
                                else:
                                    formatted_value = latest_value
                                logger.info(f"    ✅ {trend_type}: {formatted_value} (时间戳: {timestamp})")

                                # 存储格式化后的数据
                                try:
                                    if isinstance(latest_value, str):
                                        numeric_value = float(latest_value)
                                    else:
                                        numeric_value = latest_value
                                except (ValueError, TypeError):
                                    numeric_value = latest_value

                                if trend_type in ['top100_holder_percent', 'top10_holder_percent', 'bluechip_owner_percentage'] and isinstance(numeric_value, (int, float)):
                                    result[f'gmgn_{trend_type}'] = f"{numeric_value * 100:.1f}"
                                elif trend_type == 'avg_holding_balance' and isinstance(numeric_value, (int, float)):
                                    result[f'gmgn_{trend_type}'] = f"{int(numeric_value)}"
                                else:
                                    result[f'gmgn_{trend_type}'] = latest_value
                            else:
                                logger.info(f"    ✅ {trend_type}: {latest_data}")
                                result[f'gmgn_{trend_type}'] = latest_data
                        else:
                            logger.info(f"    ✅ {trend_type}: null")
                            result[f'gmgn_{trend_type}'] = 'null'

                    # 处理holder_count（不显示但仍提取数据）
                    if 'holder_count' in trends_data:
                        holder_count_list = trends_data['holder_count']
                        if isinstance(holder_count_list, list) and len(holder_count_list) > 0:
                            latest_data = holder_count_list[-1]
                            if isinstance(latest_data, dict) and 'value' in latest_data:
                                result['gmgn_holder_count'] = latest_data['value']

            # 提取wallet_tags数据 - 完整版本
            if 'wallet_tags' in all_api_data and all_api_data['wallet_tags']:
                wallet_data = all_api_data['wallet_tags'].get('data', {})

                # 输出详细的WALLET_TAGS字段展示
                if wallet_data:
                    logger.info(f"📊 WALLET_TAGS 数据展示:")

                    count = 0
                    # 定义需要过滤的字段（避免重复显示）
                    filtered_fields = {'chain', 'token_address'}

                    for key, value in wallet_data.items():
                        # 过滤掉指定字段
                        if key in filtered_fields:
                            continue
                        if count >= 5:  # 显示前5个字段
                            break

                        # 处理不同类型的值
                        if value is None:
                            display_value = "null"
                        elif isinstance(value, str):
                            if len(value) > 50:
                                display_value = value[:50] + "..."
                            elif value == "":
                                display_value = "null"
                            else:
                                display_value = value
                        elif isinstance(value, (list, dict)):
                            if len(value) == 0:
                                display_value = "null"
                            else:
                                display_value = f"{type(value).__name__}(长度: {len(value)})"
                        elif value == 0 and key in ['price', 'market_cap', 'volume_24h']:
                            display_value = "null"  # 价格相关字段为0时显示null
                        else:
                            display_value = value

                        logger.info(f"  ✅ {key}: {display_value}")
                        count += 1

                    # 显示剩余字段数量
                    remaining_count = len([k for k in wallet_data.keys() if k not in filtered_fields]) - count
                    if remaining_count > 0:
                        logger.info(f"  ... 还有 {remaining_count} 个字段")

                # 提取数据到结果中（保留原有逻辑并添加缺失的4个字段）
                result['gmgn_smart_wallets'] = wallet_data.get('smart_wallets', 'null')
                result['gmgn_fresh_wallets'] = wallet_data.get('fresh_wallets', 'null')
                result['gmgn_renowned_wallets'] = wallet_data.get('renowned_wallets', 'null')
                result['gmgn_creator_wallets'] = wallet_data.get('creator_wallets', 'null')
                result['gmgn_sniper_wallets'] = wallet_data.get('sniper_wallets', 'null')
                result['gmgn_rat_trader_wallets'] = wallet_data.get('rat_trader_wallets', 'null')
                result['gmgn_whale_wallets'] = wallet_data.get('whale_wallets', 'null')
                result['gmgn_top_wallets'] = wallet_data.get('top_wallets', 'null')
                result['gmgn_following_wallets'] = wallet_data.get('following_wallets', 'null')
                result['gmgn_bundler_wallets'] = wallet_data.get('bundler_wallets', 'null')

            # 提取multi_window数据 - 完整版本
            if 'multi_window' in all_api_data and all_api_data['multi_window']:
                multi_data = all_api_data['multi_window'].get('data', [])
                if isinstance(multi_data, list) and len(multi_data) > 0:
                    token_data = multi_data[0]

                    # 输出详细的MULTI_WINDOW关键数据展示
                    logger.info(f"📝 关键数据展示:")

                    # 计算 twitter change
                    twitter_change = 0
                    if 'dev' in token_data and isinstance(token_data['dev'], dict):
                        twitter_history = token_data['dev'].get('twitter_name_change_history', [])
                        if isinstance(twitter_history, list):
                            # 计算非空 address 的数量
                            twitter_change = sum(1 for item in twitter_history
                                               if isinstance(item, dict) and
                                               item.get('address', '').strip() != '')
                        else:
                            twitter_change = 'null'
                    else:
                        twitter_change = 'null'

                    logger.info(f"    ✅ twitter change: {twitter_change}")

                    # 显示其他关键字段
                    key_fields = [
                        ('dev.creator_token_balance', ['dev', 'creator_token_balance']),
                        ('dev.dexscr_ad', ['dev', 'dexscr_ad']),
                        ('dev.cto_flag', ['dev', 'cto_flag']),
                        ('dev.twitter_del_post_token_count', ['dev', 'twitter_del_post_token_count']),
                        ('dev.twitter_create_token_count', ['dev', 'twitter_create_token_count']),
                        ('price.price', ['price', 'price']),
                        ('price.price_1m', ['price', 'price_1m']),
                        ('price.buys_1m', ['price', 'buys_1m']),
                        ('price.buys_5m', ['price', 'buys_5m']),
                        ('price.sells_1m', ['price', 'sells_1m']),
                        ('price.sells_5m', ['price', 'sells_5m']),
                        ('price.volume_1m', ['price', 'volume_1m']),
                        ('price.volume_5m', ['price', 'volume_5m']),
                        ('price.buy_volume_1m', ['price', 'buy_volume_1m']),
                        ('price.buy_volume_5m', ['price', 'buy_volume_5m']),
                        ('price.sell_volume_1m', ['price', 'sell_volume_1m']),
                        ('price.sell_volume_5m', ['price', 'sell_volume_5m']),
                        ('price.hot_level', ['price', 'hot_level'])
                    ]

                    for display_name, path in key_fields:
                        value = token_data
                        try:
                            for key in path:
                                if isinstance(value, dict) and key in value:
                                    value = value[key]
                                else:
                                    value = 'null'
                                    break

                            # 处理显示值
                            if value == 'null' or value is None:
                                display_value = 'null'
                            elif isinstance(value, str) and value.strip() == '':
                                display_value = 'null'
                            elif value == 0 and any(x in display_name for x in ['price', 'volume']):
                                display_value = 'null'
                            else:
                                # 先尝试转换为数字
                                try:
                                    if isinstance(value, str):
                                        numeric_value = float(value)
                                    else:
                                        numeric_value = value
                                except (ValueError, TypeError):
                                    numeric_value = value

                                # 特殊处理需要除以10000000的字段
                                if display_name == 'dev.creator_token_balance' and isinstance(numeric_value, (int, float)):
                                    display_value = f"{numeric_value / 10000000:.1f}"
                                # 特殊处理需要乘以1000000000，显示为整数的字段
                                elif display_name in ['price.price', 'price.price_1m'] and isinstance(numeric_value, (int, float)):
                                    display_value = f"{int(numeric_value * 1000000000)}"
                                # 特殊处理需要去除小数点的字段
                                elif display_name in ['price.volume_1m', 'price.volume_5m', 'price.buy_volume_1m', 'price.buy_volume_5m', 'price.sell_volume_1m', 'price.sell_volume_5m'] and isinstance(numeric_value, (int, float)):
                                    display_value = f"{int(numeric_value)}"
                                else:
                                    display_value = value

                            logger.info(f"    ✅ {display_name}: {display_value}")
                        except:
                            logger.info(f"    ✅ {display_name}: null")

                    # 提取数据到结果中（应用格式化处理）
                    if 'price' in token_data:
                        price_data = token_data['price']

                        # 特殊处理需要乘以1000000000显示为整数的字段
                        price_value = price_data.get('price', 'null')
                        if price_value != 'null':
                            try:
                                if isinstance(price_value, str):
                                    numeric_value = float(price_value)
                                else:
                                    numeric_value = price_value

                                if isinstance(numeric_value, (int, float)):
                                    result['gmgn_price'] = f"{int(numeric_value * 1000000000)}"
                                else:
                                    result['gmgn_price'] = price_value
                            except (ValueError, TypeError):
                                result['gmgn_price'] = price_value
                        else:
                            result['gmgn_price'] = price_value

                        price_1m_value = price_data.get('price_1m', 'null')
                        if price_1m_value != 'null':
                            try:
                                if isinstance(price_1m_value, str):
                                    numeric_value = float(price_1m_value)
                                else:
                                    numeric_value = price_1m_value

                                if isinstance(numeric_value, (int, float)):
                                    result['gmgn_price_1m'] = f"{int(numeric_value * 1000000000)}"
                                else:
                                    result['gmgn_price_1m'] = price_1m_value
                            except (ValueError, TypeError):
                                result['gmgn_price_1m'] = price_1m_value
                        else:
                            result['gmgn_price_1m'] = price_1m_value

                        result['gmgn_buys_5m'] = price_data.get('buys_5m', 'null')
                        result['gmgn_sells_5m'] = price_data.get('sells_5m', 'null')

                        # 特殊处理需要去除小数点显示为整数的字段
                        volume_5m_value = price_data.get('volume_5m', 'null')
                        if volume_5m_value != 'null':
                            try:
                                if isinstance(volume_5m_value, str):
                                    numeric_value = float(volume_5m_value)
                                else:
                                    numeric_value = volume_5m_value

                                if isinstance(numeric_value, (int, float)):
                                    result['gmgn_volume_5m'] = f"{int(numeric_value)}"
                                else:
                                    result['gmgn_volume_5m'] = volume_5m_value
                            except (ValueError, TypeError):
                                result['gmgn_volume_5m'] = volume_5m_value
                        else:
                            result['gmgn_volume_5m'] = volume_5m_value

                        buy_volume_5m_value = price_data.get('buy_volume_5m', 'null')
                        if buy_volume_5m_value != 'null':
                            try:
                                if isinstance(buy_volume_5m_value, str):
                                    numeric_value = float(buy_volume_5m_value)
                                else:
                                    numeric_value = buy_volume_5m_value

                                if isinstance(numeric_value, (int, float)):
                                    result['gmgn_buy_volume_5m'] = f"{int(numeric_value)}"
                                else:
                                    result['gmgn_buy_volume_5m'] = buy_volume_5m_value
                            except (ValueError, TypeError):
                                result['gmgn_buy_volume_5m'] = buy_volume_5m_value
                        else:
                            result['gmgn_buy_volume_5m'] = buy_volume_5m_value

                        sell_volume_5m_value = price_data.get('sell_volume_5m', 'null')
                        if sell_volume_5m_value != 'null':
                            try:
                                if isinstance(sell_volume_5m_value, str):
                                    numeric_value = float(sell_volume_5m_value)
                                else:
                                    numeric_value = sell_volume_5m_value

                                if isinstance(numeric_value, (int, float)):
                                    result['gmgn_sell_volume_5m'] = f"{int(numeric_value)}"
                                else:
                                    result['gmgn_sell_volume_5m'] = sell_volume_5m_value
                            except (ValueError, TypeError):
                                result['gmgn_sell_volume_5m'] = sell_volume_5m_value
                        else:
                            result['gmgn_sell_volume_5m'] = sell_volume_5m_value

                        # 添加新的字段（同样应用格式化）
                        result['gmgn_buys_1m'] = price_data.get('buys_1m', 'null')
                        result['gmgn_sells_1m'] = price_data.get('sells_1m', 'null')

                        volume_1m_value = price_data.get('volume_1m', 'null')
                        if volume_1m_value != 'null':
                            try:
                                if isinstance(volume_1m_value, str):
                                    numeric_value = float(volume_1m_value)
                                else:
                                    numeric_value = volume_1m_value

                                if isinstance(numeric_value, (int, float)):
                                    result['gmgn_volume_1m'] = f"{int(numeric_value)}"
                                else:
                                    result['gmgn_volume_1m'] = volume_1m_value
                            except (ValueError, TypeError):
                                result['gmgn_volume_1m'] = volume_1m_value
                        else:
                            result['gmgn_volume_1m'] = volume_1m_value

                        buy_volume_1m_value = price_data.get('buy_volume_1m', 'null')
                        if buy_volume_1m_value != 'null':
                            try:
                                if isinstance(buy_volume_1m_value, str):
                                    numeric_value = float(buy_volume_1m_value)
                                else:
                                    numeric_value = buy_volume_1m_value

                                if isinstance(numeric_value, (int, float)):
                                    result['gmgn_buy_volume_1m'] = f"{int(numeric_value)}"
                                else:
                                    result['gmgn_buy_volume_1m'] = buy_volume_1m_value
                            except (ValueError, TypeError):
                                result['gmgn_buy_volume_1m'] = buy_volume_1m_value
                        else:
                            result['gmgn_buy_volume_1m'] = buy_volume_1m_value

                        sell_volume_1m_value = price_data.get('sell_volume_1m', 'null')
                        if sell_volume_1m_value != 'null':
                            try:
                                if isinstance(sell_volume_1m_value, str):
                                    numeric_value = float(sell_volume_1m_value)
                                else:
                                    numeric_value = sell_volume_1m_value

                                if isinstance(numeric_value, (int, float)):
                                    result['gmgn_sell_volume_1m'] = f"{int(numeric_value)}"
                                else:
                                    result['gmgn_sell_volume_1m'] = sell_volume_1m_value
                            except (ValueError, TypeError):
                                result['gmgn_sell_volume_1m'] = sell_volume_1m_value
                        else:
                            result['gmgn_sell_volume_1m'] = sell_volume_1m_value

                        result['gmgn_hot_level'] = price_data.get('hot_level', 'null')

                    # 添加dev相关字段（应用格式化处理）
                    if 'dev' in token_data:
                        dev_data = token_data['dev']

                        # 特殊处理需要除以10000000保留1位小数的字段
                        creator_balance = dev_data.get('creator_token_balance', 'null')
                        if creator_balance != 'null':
                            try:
                                if isinstance(creator_balance, str):
                                    numeric_value = float(creator_balance)
                                else:
                                    numeric_value = creator_balance

                                if isinstance(numeric_value, (int, float)):
                                    result['gmgn_creator_token_balance'] = f"{numeric_value / 10000000:.1f}"
                                else:
                                    result['gmgn_creator_token_balance'] = creator_balance
                            except (ValueError, TypeError):
                                result['gmgn_creator_token_balance'] = creator_balance
                        else:
                            result['gmgn_creator_token_balance'] = creator_balance

                        result['gmgn_dexscr_ad'] = dev_data.get('dexscr_ad', 'null')
                        result['gmgn_cto_flag'] = dev_data.get('cto_flag', 'null')
                        result['gmgn_twitter_del_post_token_count'] = dev_data.get('twitter_del_post_token_count', 'null')
                        result['gmgn_twitter_create_token_count'] = dev_data.get('twitter_create_token_count', 'null')

                    # 添加twitter change
                    result['gmgn_twitter_change'] = twitter_change

            # 提取top_buyers数据 - 完整版本
            if 'top_buyers' in all_api_data and all_api_data['top_buyers']:
                top_buyers_response = all_api_data['top_buyers'].get('data', {})

                # 检查数据结构
                if 'holders' in top_buyers_response:
                    holders_data = top_buyers_response['holders']

                    # 如果holders是列表，统计数量
                    if isinstance(holders_data, list):
                        result['gmgn_top_buyers_count'] = len(holders_data)
                    # 如果holders是字典且包含statusNow
                    elif isinstance(holders_data, dict) and 'statusNow' in holders_data:
                        status_now = holders_data['statusNow']
                        logger.info(f"📊 TOP_BUYERS 状态数据:")

                        # 定义要显示的字段及其顺序
                        top_buyers_fields = [
                            'hold',
                            'bought_more',
                            'transfered',
                            'bought_rate',
                            'holding_rate',
                            'smart_pos',
                            'smart_count_hold',
                            'smart_count_bought_more',
                            'smart_count_transfered',
                            'sold_diff',
                            'hold_diff'
                        ]

                        for field in top_buyers_fields:
                            if field in status_now:
                                value = status_now[field]
                                # 处理不同类型的值
                                if value is None:
                                    display_value = "null"
                                elif field in ['bought_rate', 'holding_rate']:
                                    # 特殊字段直接进入数字处理逻辑，跳过字符串处理
                                    pass
                                elif isinstance(value, str):
                                    if value.strip() == "":
                                        display_value = "null"
                                    else:
                                        display_value = value
                                elif isinstance(value, list):
                                    if len(value) == 0:
                                        display_value = "null"  # 空数组显示为null而不是[]
                                    else:
                                        display_value = f"list(长度: {len(value)})"

                                # 数字处理逻辑（包括特殊字段）
                                if field in ['bought_rate', 'holding_rate'] or (value is not None and not isinstance(value, str) and not isinstance(value, list)):
                                    # 特殊处理需要乘以100的字段
                                    if field in ['bought_rate', 'holding_rate']:
                                        # 先尝试转换为数字
                                        try:
                                            if isinstance(value, str):
                                                numeric_value = float(value)
                                            else:
                                                numeric_value = value

                                            if isinstance(numeric_value, (int, float)):
                                                display_value = f"{numeric_value * 100:.1f}"
                                            else:
                                                display_value = value
                                        except (ValueError, TypeError):
                                            display_value = value
                                    else:
                                        display_value = value
                                logger.info(f"    ✅ {field}: {display_value}")

                                # 将状态数据添加到结果中（应用格式化处理）
                                if field in ['bought_rate', 'holding_rate'] and value is not None:
                                    try:
                                        if isinstance(value, str):
                                            numeric_value = float(value)
                                        else:
                                            numeric_value = value

                                        if isinstance(numeric_value, (int, float)):
                                            result[f'gmgn_top_buyers_{field}'] = f"{numeric_value * 100:.1f}"
                                        else:
                                            result[f'gmgn_top_buyers_{field}'] = value
                                    except (ValueError, TypeError):
                                        result[f'gmgn_top_buyers_{field}'] = value
                                else:
                                    result[f'gmgn_top_buyers_{field}'] = value if value is not None else 'null'
                            else:
                                logger.info(f"    ✅ {field}: null")
                                result[f'gmgn_top_buyers_{field}'] = 'null'

                        # 设置默认的top_buyers_count
                        result['gmgn_top_buyers_count'] = 'null'
                    else:
                        logger.info("⚠️ 未找到 statusNow 数据")
                        result['gmgn_top_buyers_count'] = 'null'
                else:
                    logger.info("⚠️ 未找到 holders 数据")
                    result['gmgn_top_buyers_count'] = 'null'

            # 提取token_holders数据 - 完整版本
            if 'token_holders' in all_api_data and all_api_data['token_holders']:
                holders_data = all_api_data['token_holders'].get('data', {}).get('list', [])
                if isinstance(holders_data, list):
                    result['gmgn_top_holders_count'] = len(holders_data)

                    # 输出详细的TOKEN_HOLDERS聚合统计
                    if len(holders_data) > 0:
                        logger.info(f"📊 TOKEN_HOLDERS:")
                        logger.info(f"  📊 TOKEN_HOLDERS 聚合统计:")

                        # 筛选条件：addr_type = 0 且 wallet_tag_v2 为 TOP77 以下（包含TOP77）
                        filtered_holders = []
                        for holder in holders_data:
                            if isinstance(holder, dict):
                                addr_type = holder.get('addr_type', None)
                                wallet_tag = holder.get('wallet_tag_v2', '')

                                # 检查 addr_type = 0
                                if addr_type == 0:
                                    # 检查 wallet_tag_v2 是否为 TOP77 以下
                                    if isinstance(wallet_tag, str) and wallet_tag.startswith('TOP'):
                                        try:
                                            tag_number = int(wallet_tag[3:])  # 提取 TOP 后面的数字
                                            if tag_number <= 77:
                                                filtered_holders.append(holder)
                                        except (ValueError, IndexError):
                                            continue

                        logger.info(f"    📋 符合条件的钱包数量: {len(filtered_holders)}")

                        # 查找 biggest holder (TOP2 的 amount_percentage)
                        biggest_holder = 0
                        for holder in holders_data:
                            if isinstance(holder, dict):
                                wallet_tag = holder.get('wallet_tag_v2', '')
                                if wallet_tag == 'TOP2':
                                    biggest_holder = holder.get('amount_percentage', 0)
                                    break

                        # 格式化 biggest holder 值（乘以100，保留1位小数）
                        if isinstance(biggest_holder, (int, float)):
                            formatted_biggest_holder = f"{biggest_holder * 100:.1f}"
                            # 存储格式化后的值
                            result['gmgn_biggest_holder'] = formatted_biggest_holder
                        else:
                            formatted_biggest_holder = biggest_holder
                            result['gmgn_biggest_holder'] = biggest_holder
                        logger.info(f"    ✅ biggest holder: {formatted_biggest_holder}")

                        # 统计各项指标
                        stats = {
                            'native_balance_ge_6b': 0,  # native_balance >= 6000000000
                            'native_transfer_names': {},  # native_transfer.name 统计
                            'tags_photon_count': 0,  # tags 中包含 photon 的次数
                            'maker_token_tags': {},  # maker_token_tags 统计
                            'transfer_in_true': 0,  # transfer_in: true 次数
                            'is_new_true': 0,  # is_new: true 次数
                            'is_suspicious_true': 0,  # is_suspicious: true 次数
                            'sandwich_bot_count': 0,  # sandwich_bot 出现次数
                            'bundler_count': 0  # bundler 出现次数
                        }

                        for holder in filtered_holders:
                            # 1. native_balance >= 6000000000
                            native_balance = holder.get('native_balance', '')
                            if isinstance(native_balance, str) and native_balance.isdigit():
                                if int(native_balance) >= 6000000000:
                                    stats['native_balance_ge_6b'] += 1
                            elif isinstance(native_balance, (int, float)):
                                if native_balance >= 6000000000:
                                    stats['native_balance_ge_6b'] += 1

                            # 2. native_transfer.name 统计
                            native_transfer = holder.get('native_transfer', {})
                            if isinstance(native_transfer, dict):
                                name = native_transfer.get('name', None)
                                if name is None:
                                    name = 'null'
                                stats['native_transfer_names'][name] = stats['native_transfer_names'].get(name, 0) + 1

                            # 3. tags 中包含 photon 的次数
                            tags = holder.get('tags', [])
                            if isinstance(tags, list):
                                if 'photon' in tags:
                                    stats['tags_photon_count'] += 1

                            # 4. maker_token_tags 统计
                            maker_token_tags = holder.get('maker_token_tags', [])
                            if isinstance(maker_token_tags, list):
                                for tag in maker_token_tags:
                                    if isinstance(tag, str):
                                        stats['maker_token_tags'][tag] = stats['maker_token_tags'].get(tag, 0) + 1

                            # 5. transfer_in: true 次数
                            if holder.get('transfer_in', False) is True:
                                stats['transfer_in_true'] += 1

                            # 6. is_new: true 次数
                            if holder.get('is_new', False) is True:
                                stats['is_new_true'] += 1

                            # 7. is_suspicious: true 次数
                            if holder.get('is_suspicious', False) is True:
                                stats['is_suspicious_true'] += 1

                            # 8. sandwich_bot 出现次数（在 tags 或其他字段中查找）
                            # 检查所有字段中是否包含 sandwich_bot
                            holder_str = str(holder).lower()
                            if 'sandwich_bot' in holder_str:
                                stats['sandwich_bot_count'] += 1

                            # 9. bundler 出现次数（在 tags 或其他字段中查找）
                            if 'bundler' in holder_str:
                                stats['bundler_count'] += 1

                        # 输出统计结果
                        logger.info(f"    ✅ native_balance >= 6000000000 的个数: {stats['native_balance_ge_6b']}")

                        logger.info(f"    ✅ native_transfer.name 统计:")
                        for name, count in stats['native_transfer_names'].items():
                            logger.info(f"      • {name}: {count}")

                        logger.info(f"    ✅ tags 中包含 photon 的次数: {stats['tags_photon_count']}")

                        logger.info(f"    ✅ maker_token_tags 统计:")
                        if stats['maker_token_tags']:
                            for tag, count in stats['maker_token_tags'].items():
                                logger.info(f"      • {tag}: {count}")
                        else:
                            logger.info(f"      • 无数据")

                        logger.info(f"    ✅ transfer_in: true 出现次数: {stats['transfer_in_true']}")
                        logger.info(f"    ✅ is_new: true 出现次数: {stats['is_new_true']}")
                        logger.info(f"    ✅ is_suspicious: true 出现次数: {stats['is_suspicious_true']}")
                        logger.info(f"    ✅ sandwich_bot 出现次数: {stats['sandwich_bot_count']}")
                        logger.info(f"    ✅ bundler 出现次数: {stats['bundler_count']}")

                        # 将统计数据添加到结果中
                        result['gmgn_filtered_holders_count'] = len(filtered_holders)
                        result['gmgn_native_balance_ge_6b'] = stats['native_balance_ge_6b']
                        result['gmgn_tags_photon_count'] = stats['tags_photon_count']
                        result['gmgn_transfer_in_true'] = stats['transfer_in_true']
                        result['gmgn_is_new_true'] = stats['is_new_true']
                        result['gmgn_is_suspicious_true'] = stats['is_suspicious_true']
                        result['gmgn_sandwich_bot_count'] = stats['sandwich_bot_count']
                        result['gmgn_bundler_count'] = stats['bundler_count']

                        # 处理native_transfer_names统计
                        if stats['native_transfer_names']:
                            for name, count in stats['native_transfer_names'].items():
                                safe_name = name.replace(' ', '_').replace('-', '_').replace('.', '_')
                                result[f'gmgn_native_transfer_{safe_name}'] = count

                        # 处理maker_token_tags统计
                        if stats['maker_token_tags']:
                            for tag, count in stats['maker_token_tags'].items():
                                safe_tag = tag.replace(' ', '_').replace('-', '_').replace('.', '_')
                                result[f'gmgn_maker_token_tag_{safe_tag}'] = count

                    # 统计特殊钱包（保留原有逻辑）
                    suspicious_count = sum(1 for holder in holders_data if holder.get('is_suspicious', False))
                    result['gmgn_suspicious_holders'] = suspicious_count
                else:
                    logger.info("  ⚠️ 未找到有效的 holders 列表数据")

            logger.info(f"GMGN数据提取完成，获取到 {len(result)} 个字段")
            return result

        except Exception as e:
            logger.error(f"❌ GMGN数据提取失败: {e}")
            return {}

    async def call_api_async(self, api_func, api_name, token_address):
        """异步调用API的包装函数"""
        def run_api():
            return api_func(token_address)

        # 在线程池中运行同步API调用
        loop = asyncio.get_event_loop()
        with ThreadPoolExecutor() as executor:
            result = await loop.run_in_executor(executor, run_api)

        return api_name, result

    def get_gmgn_data_concurrent(self, token_address: str) -> Dict[str, Any]:
        """并发获取GMGN相关数据"""
        if not GMGN_AVAILABLE:
            logger.warning("GMGN API不可用，返回空数据")
            return {}

        try:
            logger.info(f"开始并发获取GMGN数据: {token_address}")

            # 定义所有API调用任务
            api_tasks = [
                (self.call_gmgn_token_stat_api, "token_stat"),
                (self.call_gmgn_token_trends_api, "token_trends"),
                (self.call_gmgn_wallet_tags_api, "wallet_tags"),
                (self.call_gmgn_mutil_window_api, "multi_window"),
                (self.call_gmgn_top_buyers_api, "top_buyers"),
                (self.call_gmgn_token_holders_api, "token_holders")
            ]

            # 使用线程池并发执行
            all_results = {}
            success_count = 0

            with concurrent.futures.ThreadPoolExecutor(max_workers=6) as executor:
                # 提交所有任务
                future_to_api = {
                    executor.submit(api_func, token_address): api_name
                    for api_func, api_name in api_tasks
                }

                # 等待所有任务完成
                for future in concurrent.futures.as_completed(future_to_api):
                    api_name = future_to_api[future]
                    try:
                        result = future.result()
                        if result:
                            all_results[api_name] = result
                            success_count += 1
                            logger.debug(f"✅ GMGN {api_name} API 成功")
                        else:
                            logger.debug(f"❌ GMGN {api_name} API 失败")
                    except Exception as e:
                        logger.error(f"❌ GMGN {api_name} API 异常: {e}")

            logger.info(f"GMGN API并发调用完成，成功: {success_count}/6")

            # 提取数据
            if all_results:
                extracted_data = self.extract_gmgn_data(all_results, token_address)
                logger.info(f"GMGN数据提取完成，获取到 {len(extracted_data)} 个字段")
                return extracted_data
            else:
                logger.warning("未获取到任何GMGN数据")
                return {}

        except Exception as e:
            logger.error(f"GMGN API并发调用出错: {e}")
            return {}

    def get_pool_status(self):
        """获取连接池状态信息"""
        status = {
            'current_pool': self.current_pool_index + 1,
            'total_pools': self.pool_size,
            'pool_health': [],
            'pool_ages': [],
            'consecutive_failures': []
        }

        current_time = time.time()
        for i in range(self.pool_size):
            age_hours = (current_time - self.pool_created_time[i]) / 3600
            status['pool_health'].append(self.pool_health[i])
            status['pool_ages'].append(f"{age_hours:.1f}h")
            status['consecutive_failures'].append(self.consecutive_failures[i])

        return status

    def log_pool_status(self):
        """记录连接池状态"""
        status = self.get_pool_status()
        logger.info(f"📊 GMGN连接池状态:")
        logger.info(f"   当前池: {status['current_pool']}/{status['total_pools']}")
        for i in range(status['total_pools']):
            health_icon = "✅" if status['pool_health'][i] else "❌"
            logger.info(f"   池 {i+1}: {health_icon} 年龄:{status['pool_ages'][i]} 失败:{status['consecutive_failures'][i]}")


# 全局GMGN客户端单例
_gmgn_client_instance = None

def get_gmgn_client():
    """获取GMGN客户端单例"""
    global _gmgn_client_instance
    if _gmgn_client_instance is None:
        _gmgn_client_instance = GmgnApiClient()
        logger.info("🔄 创建GMGN客户端单例")
    return _gmgn_client_instance

def get_ave_info(token_address, result_dict):
    """从Ave.ai API获取多个指标值"""
    try:
        logger.info(f"使用API获取Ave.ai数据: token_address={token_address}")

        # 创建API客户端
        api_client = AveApiClient()

        # 并发获取Ave.ai数据
        ave_data = api_client.get_ave_data_concurrent(token_address)

        # 将获取到的数据添加到结果字典中
        if ave_data:
            result_dict.update(ave_data)
            logger.info(f"Ave.ai API获取完成，结果: {', '.join([f'{k}={v}' for k, v in ave_data.items()])}")
        else:
            logger.warning("未获取到Ave.ai数据，设置默认值")
            # 设置默认值
            default_fields = [
                'ave_cabal', 'ave_phishing', 'ave_insiders', 'ave_bundle',
                'ave_snipers', 'ave_dev', 'ave_whale', 'ave_smarters', 'ave_kol',
                'ave_rug_pull_stats', 'ave_insiders_rate', 'ave_phishing_rate', 'ave_cabal_rate',
                'ave_bundle_rate', 'ave_all_tag_rate', 'ave_price_change_5m', 'ave_buy_volume_5m',
                'ave_sell_volume_5m', 'ave_total_volume_5m', 'ave_buy_tx_5m', 'ave_sell_tx_5m',
                'ave_buyers_5m', 'ave_sellers_5m', 'ave_smart_buy_24h', 'ave_smart_sell_24h'
            ]
            for field in default_fields:
                result_dict[field] = 'null'

    except Exception as e:
        logger.error(f"获取Ave.ai API数据时出错: {e}")
        # 设置所有字段为默认值
        default_fields = [
            'ave_cabal', 'ave_phishing', 'ave_insiders', 'ave_bundle',
            'ave_snipers', 'ave_dev', 'ave_whale', 'ave_smarters', 'ave_kol',
            'ave_rug_pull_stats', 'ave_insiders_rate', 'ave_phishing_rate', 'ave_cabal_rate',
            'ave_bundle_rate', 'ave_all_tag_rate', 'ave_price_change_5m', 'ave_buy_volume_5m',
            'ave_sell_volume_5m', 'ave_total_volume_5m', 'ave_buy_tx_5m', 'ave_sell_tx_5m',
            'ave_buyers_5m', 'ave_sellers_5m', 'ave_smart_buy_24h', 'ave_smart_sell_24h'
        ]
        for field in default_fields:
            result_dict[field] = 'null'

def get_axiom_info(bonding_curve, creator, result_dict):
    """从Axiom API获取多个指标值 - 使用API替换网页爬取"""
    try:
        logger.info(f"使用API获取Axiom数据: bonding_curve={bonding_curve}, creator={creator}")

        # 创建API客户端
        api_client = AxiomAPIClient()

        # 并发获取Axiom数据
        axiom_data = api_client.get_axiom_data_concurrent(bonding_curve, creator)

        # 将获取到的数据添加到结果字典中
        if axiom_data:
            result_dict.update(axiom_data)
            logger.info(f"Axiom API获取完成，结果: {', '.join([f'{k}={v}' for k, v in axiom_data.items()])}")
        else:
            logger.warning("未获取到Axiom数据，设置默认值")
            # 设置默认值
            default_fields = [
                'axiom_totalCount', 'axiom_migratedCount', 'axiom_numHolders', 'axiom_numBotUsers',
                'axiom_top10HoldersPercent', 'axiom_devHoldsPercent', 'axiom_insidersHoldPercent',
                'axiom_bundlersHoldPercent', 'axiom_snipersHoldPercent', 'axiom_totalPairFeesPaid'
            ]
            for field in default_fields:
                result_dict[field] = 'null'

    except Exception as e:
        logger.error(f"获取Axiom API数据时出错: {e}")
        # 设置所有字段为默认值
        default_fields = [
            'axiom_totalCount', 'axiom_migratedCount', 'axiom_numHolders', 'axiom_numBotUsers',
            'axiom_top10HoldersPercent', 'axiom_devHoldsPercent', 'axiom_insidersHoldPercent',
            'axiom_bundlersHoldPercent', 'axiom_snipersHoldPercent', 'axiom_totalPairFeesPaid'
        ]
        for field in default_fields:
            result_dict[field] = 'null'

def get_gmgn_info(token_address, result_dict):
    """从GMGN API获取多个指标值 - 使用连接池轮换机制"""
    try:
        logger.info(f"使用API获取GMGN数据: token_address={token_address}")

        # 使用全局单例客户端（连接池轮换版本）
        api_client = get_gmgn_client()

        # 并发获取GMGN数据
        gmgn_data = api_client.get_gmgn_data_concurrent(token_address)

        # 将获取到的数据添加到结果字典中
        if gmgn_data:
            result_dict.update(gmgn_data)
            logger.info(f"GMGN API获取完成，结果: {', '.join([f'{k}={v}' for k, v in gmgn_data.items()])}")
        else:
            logger.warning("未获取到GMGN数据，设置默认值")
            # 设置默认值 - 包含所有GMGN统计字段
            default_fields = [
                'gmgn_holder_count', 'gmgn_bluechip_owner_percentage', 'gmgn_signal_count', 'gmgn_degen_call_count',
                'gmgn_top_rat_trader_percentage', 'gmgn_top_bundler_trader_percentage', 'gmgn_top_entrapment_trader_percentage',
                'gmgn_avg_holding_balance', 'gmgn_top10_holder_percent', 'gmgn_top100_holder_percent',
                'gmgn_smart_wallets', 'gmgn_fresh_wallets', 'gmgn_renowned_wallets', 'gmgn_creator_wallets',
                'gmgn_sniper_wallets', 'gmgn_rat_trader_wallets', 'gmgn_whale_wallets', 'gmgn_top_wallets',
                'gmgn_following_wallets', 'gmgn_bundler_wallets', 'gmgn_price', 'gmgn_price_1m', 'gmgn_buys_5m', 'gmgn_sells_5m',
                'gmgn_volume_5m', 'gmgn_buy_volume_5m', 'gmgn_sell_volume_5m', 'gmgn_top_buyers_count',
                'gmgn_top_holders_count', 'gmgn_suspicious_holders',
                # token_holders详细统计字段
                'gmgn_filtered_holders_count', 'gmgn_native_balance_ge_6b', 'gmgn_tags_photon_count',
                'gmgn_transfer_in_true', 'gmgn_is_new_true', 'gmgn_is_suspicious_true',
                'gmgn_sandwich_bot_count', 'gmgn_bundler_count', 'gmgn_biggest_holder',
                # multi_window新增字段
                'gmgn_buys_1m', 'gmgn_sells_1m', 'gmgn_volume_1m', 'gmgn_buy_volume_1m', 'gmgn_sell_volume_1m',
                'gmgn_hot_level', 'gmgn_creator_token_balance', 'gmgn_dexscr_ad', 'gmgn_cto_flag', 'gmgn_twitter_change',
                'gmgn_twitter_del_post_token_count', 'gmgn_twitter_create_token_count',
                # top_buyers状态字段
                'gmgn_top_buyers_hold', 'gmgn_top_buyers_bought_more', 'gmgn_top_buyers_transfered',
                'gmgn_top_buyers_bought_rate', 'gmgn_top_buyers_holding_rate', 'gmgn_top_buyers_smart_pos',
                'gmgn_top_buyers_smart_count_hold', 'gmgn_top_buyers_smart_count_bought_more',
                'gmgn_top_buyers_smart_count_transfered', 'gmgn_top_buyers_sold_diff', 'gmgn_top_buyers_hold_diff'
            ]
            for field in default_fields:
                result_dict[field] = 'null'

    except Exception as e:
        logger.error(f"获取GMGN API数据时出错: {e}")
        # 设置所有字段为默认值 - 包含所有GMGN统计字段
        default_fields = [
            'gmgn_holder_count', 'gmgn_bluechip_owner_percentage', 'gmgn_signal_count', 'gmgn_degen_call_count',
            'gmgn_top_rat_trader_percentage', 'gmgn_top_bundler_trader_percentage', 'gmgn_top_entrapment_trader_percentage',
            'gmgn_avg_holding_balance', 'gmgn_top10_holder_percent', 'gmgn_top100_holder_percent',
            'gmgn_smart_wallets', 'gmgn_fresh_wallets', 'gmgn_renowned_wallets', 'gmgn_creator_wallets',
            'gmgn_sniper_wallets', 'gmgn_rat_trader_wallets', 'gmgn_whale_wallets', 'gmgn_top_wallets',
            'gmgn_following_wallets', 'gmgn_bundler_wallets', 'gmgn_price', 'gmgn_price_1m', 'gmgn_buys_5m', 'gmgn_sells_5m',
            'gmgn_volume_5m', 'gmgn_buy_volume_5m', 'gmgn_sell_volume_5m', 'gmgn_top_buyers_count',
            'gmgn_top_holders_count', 'gmgn_suspicious_holders',
            # token_holders详细统计字段
            'gmgn_filtered_holders_count', 'gmgn_native_balance_ge_6b', 'gmgn_tags_photon_count',
            'gmgn_transfer_in_true', 'gmgn_is_new_true', 'gmgn_is_suspicious_true',
            'gmgn_sandwich_bot_count', 'gmgn_bundler_count', 'gmgn_biggest_holder',
            # multi_window新增字段
            'gmgn_buys_1m', 'gmgn_sells_1m', 'gmgn_volume_1m', 'gmgn_buy_volume_1m', 'gmgn_sell_volume_1m',
            'gmgn_hot_level', 'gmgn_creator_token_balance', 'gmgn_dexscr_ad', 'gmgn_cto_flag', 'gmgn_twitter_change',
            'gmgn_twitter_del_post_token_count', 'gmgn_twitter_create_token_count',
            # top_buyers状态字段
            'gmgn_top_buyers_hold', 'gmgn_top_buyers_bought_more', 'gmgn_top_buyers_transfered',
            'gmgn_top_buyers_bought_rate', 'gmgn_top_buyers_holding_rate', 'gmgn_top_buyers_smart_pos',
            'gmgn_top_buyers_smart_count_hold', 'gmgn_top_buyers_smart_count_bought_more',
            'gmgn_top_buyers_smart_count_transfered', 'gmgn_top_buyers_sold_diff', 'gmgn_top_buyers_hold_diff'
        ]
        for field in default_fields:
            result_dict[field] = 'null'

def get_dexpaid_info(token_address):
    """从DexPaid API获取代币信息"""
    try:
        url = f"https://api.dexscreener.com/orders/v1/solana/{token_address}"
        logger.info(f"请求DexPaid API: {url}")
        # 根据代理开关设置代理
        proxies = PROXY_DICT if USE_PROXY else None
        if USE_PROXY:
            logger.debug(f"DexPaid API 使用代理: {PROXY_URL}")
        response = requests.get(url, proxies=proxies, timeout=2.7)

        if response.status_code == 200:
            data = response.json()
            logger.info(f"DexPaid API返回: {data}")
            if data and isinstance(data, list) and len(data) > 0:
                for item in data:
                    if item.get("type") == "tokenProfile":
                        return {
                            "status": item.get("status", "null"),
                            "paymentTimestamp": item.get("paymentTimestamp", "null")
                        }

        return {"status": "null", "paymentTimestamp": "null"}
    except Exception as e:
        logger.error(f"获取DexPaid信息时出错: {e}")
        return {"status": "null", "paymentTimestamp": "null"}

def get_solanatracker_info(token_address):
    """从SolanaTracker API获取代币信息"""
    max_retries = 2
    retry_delay = 0.2

    for attempt in range(max_retries):
        try:
            url = f"https://data.solanatracker.io/tokens/{token_address}"
            headers = {
                'x-api-key': '621ee28e-e93d-4ec2-9e8a-17f9bb7ee023'
            }
            logger.info(f"请求SolanaTracker API: {url} (尝试 {attempt+1}/{max_retries})")
            # 根据代理开关设置代理
            proxies = PROXY_DICT if USE_PROXY else None
            if USE_PROXY:
                logger.debug(f"SolanaTracker API 使用代理: {PROXY_URL}")
            response = requests.get(url, headers=headers, proxies=proxies, timeout=3.6)

            if response.status_code == 200:
                data = response.json()
                logger.info("成功获取SolanaTracker信息")
                return data
            else:
                logger.warning(f"获取SolanaTracker信息失败，状态码: {response.status_code}")

                # 如果是最后一次尝试，记录错误并返回空字典
                if attempt == max_retries - 1:
                    logger.error("所有重试尝试均失败")
                    return {}

                # 否则等待后重试
                logger.info(f"等待 {retry_delay} 秒后重试...")
                time.sleep(retry_delay)
                retry_delay *= 0.2  # 指数退避

        except requests.exceptions.Timeout:
            logger.warning(f"请求超时 (尝试 {attempt+1}/{max_retries})")
            if attempt == max_retries - 1:
                logger.error("所有重试尝试均超时")
                return {}
            logger.info(f"等待 {retry_delay} 秒后重试...")
            time.sleep(retry_delay)
            retry_delay *= 0.2

        except Exception as e:
            logger.error(f"获取SolanaTracker信息时出错: {e}")
            return {}  # 对于非超时错误，不重试

    return {}  # 以防万一

def format_timestamp(timestamp):
    """将时间戳格式化为可读时间"""
    if not timestamp or timestamp == "null":
        return "null"
    try:
        # 转换为数字
        if isinstance(timestamp, str):
            timestamp_num = int(timestamp)
        else:
            timestamp_num = int(timestamp)

        # 判断是秒级还是毫秒级时间戳
        if timestamp_num > 1000000000000:  # 毫秒级时间戳（13位数字）
            dt = datetime.fromtimestamp(timestamp_num / 1000)
        elif timestamp_num > 1000000000:  # 秒级时间戳（10位数字）
            dt = datetime.fromtimestamp(timestamp_num)
        else:
            # 时间戳太小，可能是错误的格式
            return str(timestamp)

        return dt.strftime("%Y-%m-%d %H:%M:%S")
    except Exception as e:
        # 如果转换失败，记录错误并返回原始值
        logger.debug(f"时间戳格式化失败: {timestamp}, 错误: {e}")
        return str(timestamp)

async def process_token(token_address):
    """处理单个代币，收集所有信息"""
    logger.info(f"开始处理代币: {token_address}")

    # 创建结果字典
    detection_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    result = {
        "token_address": token_address,
        "timestamp": detection_time
    }

    # 获取SolanaTracker信息
    logger.info("获取SolanaTracker信息...")
    solanatracker_data = get_solanatracker_info(token_address)
    if solanatracker_data:
        # 提取token基础信息
        token_info = solanatracker_data.get("token", {})
        creation_info = token_info.get("creation", {})
        risk_info = solanatracker_data.get("risk", {})

        # 基础字段映射
        result.update({
            "mint": token_info.get("mint", "null"),
            "name": token_info.get("name", "null"),
            "symbol": token_info.get("symbol", "null"),
            "description": token_info.get("description", "null"),
            "twitter": token_info.get("twitter", "null"),
            "telegram": token_info.get("telegram", "null"),
            "website": token_info.get("website", "null"),
            "creator": creation_info.get("creator", "null"),
            "created_timestamp": format_timestamp(creation_info.get("created_time")),
        })

        # 提取bonding_curve (从pools中找到market为pumpfun-amm或raydium-cpmm的poolId，优先pumpfun-amm)
        bonding_curve = "null"
        pools = solanatracker_data.get("pools", [])
        # 优先查找pumpfun-amm
        for pool in pools:
            market = pool.get("market", "")
            if market == "pumpfun-amm":
                bonding_curve = pool.get("poolId", "null")
                break
        # 如果没找到pumpfun-amm，再找raydium-cpmm
        if bonding_curve == "null":
            for pool in pools:
                market = pool.get("market", "")
                if market == "raydium-cpmm":
                    bonding_curve = pool.get("poolId", "null")
                    break
        result["bonding_curve"] = bonding_curve

        # 新增字段 - 保留一位小数
        snipers_info = risk_info.get("snipers", {})
        insiders_info = risk_info.get("insiders", {})
        events_info = solanatracker_data.get("events", {})

        result.update({
            "snipers_count": snipers_info.get("count", 0),
            "snipers_percentage": round(float(snipers_info.get("totalPercentage", 0)), 1),
            "insiders_count": insiders_info.get("count", 0),
            "insiders_percentage": round(float(insiders_info.get("totalPercentage", 0)), 1),
            "top10_percentage": round(float(risk_info.get("top10", 0)), 1),
            "holders_count": solanatracker_data.get("holders", 0),
            "price_change_1m": round(float(events_info.get("1m", {}).get("priceChangePercentage", 0)), 1),
            "price_change_5m": round(float(events_info.get("5m", {}).get("priceChangePercentage", 0)), 1)
        })

        # 计算创建时间分钟差
        created_timestamp = result.get("created_timestamp", "null")
        if created_timestamp != "null":
            created_diff = calculate_time_diff(created_timestamp, detection_time)
            result["created_time_diff_minutes"] = created_diff if created_diff is not None else "null"
        else:
            result["created_time_diff_minutes"] = "null"

    # 获取DexPaid信息
    logger.info("获取DexPaid信息...")
    dexpaid_data = get_dexpaid_info(token_address)
    result.update({
        "dexpaid_status": dexpaid_data.get("status", "null"),
        "dexpaid_paymentTimestamp": format_timestamp(dexpaid_data.get("paymentTimestamp"))
    })

    # 计算DexPaid支付时间分钟差
    dexpaid_timestamp = result.get("dexpaid_paymentTimestamp", "null")
    if dexpaid_timestamp != "null":
        dexpaid_diff = calculate_time_diff(dexpaid_timestamp, detection_time)
        result["dexpaid_payment_time_diff_minutes"] = dexpaid_diff if dexpaid_diff is not None else "null"
    else:
        result["dexpaid_payment_time_diff_minutes"] = "null"

    # 并发获取Axiom和Ave.ai指标值
    bonding_curve = result.get("bonding_curve", "null")
    creator = result.get("creator", "null")

    async def get_axiom_data_async():
        """异步获取Axiom数据"""
        if bonding_curve != "null" and creator != "null":
            logger.info(f"使用bonding_curve: {bonding_curve}, creator: {creator} 获取Axiom指标值...")
            # 使用线程池执行同步函数，实现真正的异步
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(None, get_axiom_info, bonding_curve, creator, result)
        else:
            logger.info(f"bonding_curve或creator为null，无法获取Axiom指标值 (bonding_curve={bonding_curve}, creator={creator})")
            # 设置所有Axiom相关字段为默认值
            default_fields = [
                'axiom_totalCount', 'axiom_migratedCount', 'axiom_numHolders', 'axiom_numBotUsers',
                'axiom_top10HoldersPercent', 'axiom_devHoldsPercent', 'axiom_insidersHoldPercent',
                'axiom_bundlersHoldPercent', 'axiom_snipersHoldPercent', 'axiom_totalPairFeesPaid'
            ]
            for field in default_fields:
                result[field] = 'null'

    async def get_ave_data_async():
        """异步获取Ave.ai数据"""
        logger.info(f"获取Ave.ai指标值: token_address={token_address}")
        # 使用线程池执行同步函数，实现真正的异步
        loop = asyncio.get_event_loop()
        await loop.run_in_executor(None, get_ave_info, token_address, result)

    async def get_gmgn_data_async():
        """异步获取GMGN数据"""
        logger.info(f"获取GMGN指标值: token_address={token_address}")
        # 使用线程池执行同步函数，实现真正的异步
        loop = asyncio.get_event_loop()
        await loop.run_in_executor(None, get_gmgn_info, token_address, result)

    # 并发执行Axiom、Ave.ai和GMGN数据获取
    logger.info("开始并发获取Axiom、Ave.ai和GMGN数据...")
    await asyncio.gather(
        get_axiom_data_async(),
        get_ave_data_async(),
        get_gmgn_data_async()
    )
    logger.info("Axiom、Ave.ai和GMGN数据并发获取完成")

    # 延时1.2秒后运行telegram_token_finder代码
    logger.info(f"等待2秒后搜索Telegram频道中的代币信息: {token_address}")
    await asyncio.sleep(0.6)

    # 从Telegram频道中提取信息
    try:
        telegram_info = await extract_telegram_info(token_address)
        logger.info(f"从Telegram频道中提取到信息: {telegram_info}")

        # 将Telegram信息添加到结果中
        if 'rug_probability' in telegram_info:
            result['telegram_rug_probability'] = telegram_info['rug_probability']
        else:
            result['telegram_rug_probability'] = 'null'

        if 'hunter_content' in telegram_info:
            result['telegram_hunter_content'] = telegram_info['hunter_content']
            # 添加猎人频道名称信息
            if 'hunter_channel_name' in telegram_info:
                result['hunter_channel_name'] = telegram_info['hunter_channel_name']
        else:
            result['telegram_hunter_content'] = 'null'

        result['telegram_message_count'] = telegram_info.get('message_count', 0)
    except Exception as e:
        logger.error(f"从Telegram频道中提取信息时出错: {e}")
        result['telegram_rug_probability'] = 'null'
        result['telegram_hunter_content'] = 'null'
        result['telegram_message_count'] = 0

    return result

async def write_to_trading_bot_signal_file(token_address):
    """写入代币地址到交易机器人的信号文件"""
    try:
        # 交易机器人信号文件路径
        signal_file_path = r"D:\meme trade bot\token_signals.txt"

        # 使用文件锁确保并发安全

        # 先读取文件内容检查最后一个字符
        file_needs_newline = False
        try:
            if os.path.exists(signal_file_path):
                with open(signal_file_path, 'rb') as check_f:
                    check_f.seek(0, 2)  # 移动到文件末尾
                    if check_f.tell() > 0:  # 如果文件不为空
                        check_f.seek(-1, 2)  # 移动到最后一个字节
                        last_byte = check_f.read(1)
                        if last_byte != b'\n':
                            file_needs_newline = True
        except:
            pass  # 如果检查失败，继续执行

        # 写入信号文件（追加模式）
        with open(signal_file_path, 'a', encoding='utf-8') as f:
            # 如果需要，先添加换行符
            if file_needs_newline:
                f.write('\n')
            f.write(f"{token_address}\n")
            f.flush()

        logger.info(f"✅ 已写入交易信号文件: {token_address}")
        return True

    except Exception as e:
        logger.error(f"❌ 写入交易信号文件失败: {e}")
        return False

# 功能1：过滤配置和逻辑函数
def load_filter_config():
    """加载过滤配置文件"""
    try:
        config_file = "filter_config.txt"
        if not os.path.exists(config_file):
            logger.warning(f"过滤配置文件 {config_file} 不存在，功能1将不会过滤任何消息")
            return {}

        config = {}
        current_set = None

        with open(config_file, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()

                # 跳过空行和注释
                if not line or line.startswith('#'):
                    continue

                # 检查是否是集合定义
                if line.startswith('[') and line.endswith(']'):
                    current_set = line[1:-1]
                    config[current_set] = []
                    logger.debug(f"发现过滤集合: {current_set}")
                    continue

                # 添加条件到当前集合
                if current_set is not None:
                    config[current_set].append(line)
                    logger.debug(f"添加条件到 {current_set}: {line}")
                else:
                    logger.warning(f"配置文件第{line_num}行: 条件 '{line}' 不在任何集合中，已忽略")

        logger.info(f"✅ 成功加载过滤配置，共 {len(config)} 个集合")
        for set_name, conditions in config.items():
            logger.info(f"  集合 '{set_name}': {len(conditions)} 个条件")

        return config

    except Exception as e:
        logger.error(f"❌ 加载过滤配置失败: {e}")
        return {}

def parse_condition(condition_str):
    """解析单个过滤条件"""
    import re

    condition_str = condition_str.strip()

    # 处理带引号的字段名
    if condition_str.startswith('"') and '" ' in condition_str:
        quote_end = condition_str.find('" ', 1)
        field_name = condition_str[1:quote_end]
        operator_part = condition_str[quote_end + 2:].strip()
    else:
        # 区间格式: 数值1 <= 字段名 <= 数值2
        range_match = re.match(r'^(\d+(?:\.\d+)?)\s*(<=?|>=?)\s*(.+?)\s*(<=?|>=?)\s*(\d+(?:\.\d+)?)$', condition_str)
        if range_match:
            min_val, min_op, field_name, max_op, max_val = range_match.groups()
            return {
                'field': field_name.strip(),
                'type': 'range',
                'min_value': float(min_val),
                'max_value': float(max_val),
                'min_operator': min_op,
                'max_operator': max_op
            }

        # 单向比较格式: 字段名 操作符 数值
        single_match = re.match(r'^(.+?)\s*(<=?|>=?|==|!=)\s*(\d+(?:\.\d+)?)$', condition_str)
        if single_match:
            field_name, operator, value = single_match.groups()
            operator_part = f"{operator} {value}"
        else:
            # 反向格式: 数值 操作符 字段名
            reverse_match = re.match(r'^(\d+(?:\.\d+)?)\s*(<=?|>=?)\s*(.+?)$', condition_str)
            if reverse_match:
                value, operator, field_name = reverse_match.groups()
                # 转换操作符方向
                if operator == '>=':
                    operator = '<='
                elif operator == '<=':
                    operator = '>='
                elif operator == '>':
                    operator = '<'
                elif operator == '<':
                    operator = '>'
                operator_part = f"{operator} {value}"
            else:
                logger.warning(f"无法解析条件: {condition_str}")
                return None

    # 解析操作符和值
    operator_match = re.match(r'^(<=?|>=?|==|!=)\s*(\d+(?:\.\d+)?)$', operator_part)
    if not operator_match:
        logger.warning(f"无法解析操作符: {operator_part}")
        return None

    operator, value = operator_match.groups()

    return {
        'field': field_name.strip(),
        'type': 'single',
        'operator': operator,
        'value': float(value)
    }

def check_condition(token_info, condition):
    """检查单个条件是否满足，返回 (是否满足, 详细信息)"""
    if not condition:
        return False, "条件解析失败"

    field_name = condition['field']

    # 映射中文字段名到代码字段名
    actual_field = FIELD_MAPPING.get(field_name, field_name)

    # 获取字段值
    field_value = token_info.get(actual_field, 'null')

    # 处理null值 - 支持null值匹配
    is_null_value = field_value == 'null' or field_value is None

    if is_null_value:
        # 对于null值，我们需要特殊处理
        # null值可以满足 <= 操作符的条件（认为null值为0或最小值）
        logger.debug(f"字段 {field_name} ({actual_field}) 值为null，将按特殊规则处理")

        # 检查条件类型
        if condition['type'] == 'range':
            # 对于区间条件，null值满足 <= 最大值的条件
            max_val = condition['max_value']
            max_op = condition['max_operator']
            min_val = condition['min_value']
            min_op = condition['min_operator']
            
            # 构造原始条件表达式
            condition_expr = f"{min_val} {min_op} {field_name} {max_op} {max_val}"
            
            if max_op == '<=':
                logger.debug(f"null值满足区间条件的上限: null <= {max_val}")
                return True, f"null值满足条件"
            else:
                reason = f"实际值: null，条件表达式: {condition_expr}"
                logger.debug(f"null值不满足区间条件: {condition}")
                return False, reason
        elif condition['type'] == 'single':
            operator = condition['operator']
            target_value = condition['value']
            
            # 构造原始条件表达式
            condition_expr = f"{field_name} {operator} {target_value}"
            
            # null值满足 <= 操作符的条件
            if operator == '<=':
                logger.debug(f"null值满足条件: null <= {target_value}")
                return True, f"null值满足条件"
            else:
                reason = f"实际值: null，条件表达式: {condition_expr}"
                logger.debug(f"null值不满足条件: null {operator} {target_value}")
                return False, reason

        return False, f"字段 '{field_name}' 值为null，条件类型未知"

    # 转换为数值
    try:
        if isinstance(field_value, str):
            # 移除百分号等符号
            field_value = field_value.replace('%', '').replace(',', '')
            field_value = float(field_value)
        else:
            field_value = float(field_value)
    except (ValueError, TypeError):
        reason = f"字段 '{field_name}' 值 '{field_value}' 无法转换为数值"
        logger.debug(reason)
        return False, reason

    # 检查条件
    if condition['type'] == 'range':
        min_val = condition['min_value']
        max_val = condition['max_value']
        min_op = condition['min_operator']
        max_op = condition['max_operator']
        
        # 构造原始条件表达式
        condition_expr = f"{min_val} {min_op} {field_name} {max_op} {max_val}"

        # 检查最小值条件
        if min_op == '<=':
            min_satisfied = min_val <= field_value
        elif min_op == '<':
            min_satisfied = min_val < field_value
        elif min_op == '>=':
            min_satisfied = min_val >= field_value
        elif min_op == '>':
            min_satisfied = min_val > field_value
        else:
            min_satisfied = False

        # 检查最大值条件
        if max_op == '<=':
            max_satisfied = field_value <= max_val
        elif max_op == '<':
            max_satisfied = field_value < max_val
        elif max_op == '>=':
            max_satisfied = field_value >= max_val
        elif max_op == '>':
            max_satisfied = field_value > max_val
        else:
            max_satisfied = False

        result = min_satisfied and max_satisfied
        
        if result:
            logger.debug(f"区间条件 {condition_expr}: {field_value} -> 满足")
            return True, f"满足区间条件"
        else:
            # 返回具体的条件表达式和实际值
            reason = f"实际值: {field_value}，条件表达式: {condition_expr}"
            logger.debug(f"区间条件 {condition_expr}: {field_value} -> 不满足")
            return False, reason

    elif condition['type'] == 'single':
        operator = condition['operator']
        target_value = condition['value']
        
        # 构造原始条件表达式
        condition_expr = f"{field_name} {operator} {target_value}"

        if operator == '<=':
            result = field_value <= target_value
        elif operator == '<':
            result = field_value < target_value
        elif operator == '>=':
            result = field_value >= target_value
        elif operator == '>':
            result = field_value > target_value
        elif operator == '==':
            result = field_value == target_value
        elif operator == '!=':
            result = field_value != target_value
        else:
            result = False

        if result:
            logger.debug(f"单向条件 {condition_expr}: {field_value} -> 满足")
            return True, f"满足条件"
        else:
            # 返回具体的条件表达式和实际值
            reason = f"实际值: {field_value}，条件表达式: {condition_expr}"
            logger.debug(f"单向条件 {condition_expr}: {field_value} -> 不满足")
            return False, reason

    return False, f"未知的条件类型"

def check_filter_conditions(token_info):
    """检查代币信息是否满足过滤条件，返回 (是否通过, 失败原因)"""
    try:
        # 加载配置
        config = load_filter_config()
        if not config:
            reason = "没有过滤配置，不转发到群B"
            logger.debug(reason)
            return False, reason

        # 检查每个集合（OR关系）
        all_failed_reasons = []  # 收集所有失败原因
        
        for set_name, conditions in config.items():
            logger.debug(f"检查集合: {set_name}")

            # 集合内所有条件必须满足（AND关系）
            all_conditions_met = True
            first_failed_condition = None
            first_failed_reason = None

            for condition_str in conditions:
                condition = parse_condition(condition_str)
                if not condition:
                    first_failed_condition = condition_str
                    first_failed_reason = f"条件 '{condition_str}' 解析失败"
                    all_conditions_met = False
                    break

                satisfied, detail_reason = check_condition(token_info, condition)
                if not satisfied:
                    first_failed_condition = condition_str
                    first_failed_reason = detail_reason
                    all_conditions_met = False
                    break

            if all_conditions_met:
                logger.info(f"✅ 代币满足过滤集合 '{set_name}' 的所有条件，将转发到群B")
                return True, f"满足集合 '{set_name}' 的所有条件"
            else:
                # 记录这个集合的第一个失败条件
                failed_info = f"集合 '{set_name}' 中条件 '{first_failed_condition}' 未满足 - {first_failed_reason}"
                all_failed_reasons.append(failed_info)
                logger.debug(f"集合 '{set_name}' 条件不满足: {failed_info}")

        # 所有集合都不满足，返回所有集合的失败原因
        if all_failed_reasons:
            # 将所有失败原因合并，用分号分隔
            combined_reasons = "; ".join(all_failed_reasons)
            logger.debug(f"所有过滤集合条件都不满足，详细原因: {combined_reasons}")
            return False, combined_reasons
        else:
            reason = "所有过滤集合条件都不满足，但未找到具体失败原因"
            logger.debug(reason)
            return False, reason

    except Exception as e:
        reason = f"检查过滤条件时出错: {e}"
        logger.error(f"❌ {reason}")
        return False, reason

async def send_to_telegram_group_b(message, token_address=None):
    """发送消息到电报群B（过滤后的消息）"""
    try:
        # 检查群B配置
        if not TELEGRAM_CHAT_ID_GROUP_B:
            logger.warning("⚠️ 群B的CHAT_ID未配置，无法发送消息")
            return False

        # 检查telegram模块是否可用
        try:
            import telegram
            logger.info("✅ Telegram模块导入成功（群B）")
        except ImportError as import_error:
            logger.error(f"❌ Telegram 模块不可用，无法发送消息到群B: {import_error}")
            return False

        logger.info("🚀 开始发送消息到Telegram群B...")
        logger.info(f"📱 目标群组B ID: {TELEGRAM_CHAT_ID_GROUP_B}")
        logger.info(f"🤖 Bot Token: {TELEGRAM_TOKEN[:10]}...")
        logger.info(f"📝 消息长度: {len(message)} 字符")

        # 清理消息中的问题字符
        try:
            # 移除代理字符和其他问题字符
            cleaned_message = message.encode('utf-8', errors='ignore').decode('utf-8')
            # 移除零宽字符和其他特殊字符
            import re
            cleaned_message = re.sub(r'[\u200d\ufeff\u200b-\u200f\u2060-\u206f]', '', cleaned_message)
            logger.info("✅ 消息编码检查通过（群B）")
        except Exception as encoding_error:
            logger.error(f"❌ 消息编码处理失败（群B）: {encoding_error}")
            return False

        # 根据代理开关设置请求配置（复用群A的逻辑）
        try:
            # 尝试不同版本的导入方式
            try:
                from telegram.request import HTTPXRequest
            except ImportError:
                try:
                    from telegram.ext import HTTPXRequest
                except ImportError:
                    # 如果都导入失败，使用简单的Bot初始化
                    HTTPXRequest = None

            if USE_PROXY:
                # 尝试多种代理配置方式
                try:
                    if HTTPXRequest:
                        # 方式1: 使用proxy_url参数
                        try:
                            request = HTTPXRequest(
                                connection_pool_size=8,
                                proxy_url=PROXY_URL
                            )
                            bot_b = telegram.Bot(token=TELEGRAM_TOKEN, request=request)
                            logger.info(f"🌐 群B使用代理发送(proxy_url): {PROXY_URL}")
                        except Exception as e1:
                            logger.warning(f"群B proxy_url方式失败: {e1}")
                            # 方式2: 使用proxy参数
                            try:
                                request = HTTPXRequest(
                                    connection_pool_size=8,
                                    proxy=PROXY_URL
                                )
                                bot_b = telegram.Bot(token=TELEGRAM_TOKEN, request=request)
                                logger.info(f"🌐 群B使用代理发送(proxy): {PROXY_URL}")
                            except Exception as e2:
                                logger.warning(f"群B proxy方式失败: {e2}")
                                # 方式3: 简单Bot + 环境变量代理
                                import os
                                os.environ['HTTP_PROXY'] = PROXY_URL
                                os.environ['HTTPS_PROXY'] = PROXY_URL
                                bot_b = telegram.Bot(token=TELEGRAM_TOKEN)
                                logger.info(f"🌐 群B使用环境变量代理: {PROXY_URL}")
                    else:
                        # 如果HTTPXRequest不可用，使用环境变量
                        import os
                        os.environ['HTTP_PROXY'] = PROXY_URL
                        os.environ['HTTPS_PROXY'] = PROXY_URL
                        bot_b = telegram.Bot(token=TELEGRAM_TOKEN)
                        logger.info(f"🌐 群B使用环境变量代理: {PROXY_URL}")
                except Exception as e:
                    logger.error(f"群B代理配置失败: {e}")
                    raise
            else:
                # 直连配置
                if HTTPXRequest:
                    request = HTTPXRequest(connection_pool_size=8)
                    bot_b = telegram.Bot(token=TELEGRAM_TOKEN, request=request)
                    logger.info("🌐 群B直连发送（无代理）")
                else:
                    bot_b = telegram.Bot(token=TELEGRAM_TOKEN)
                    logger.info("🌐 群B使用简单Bot配置")
        except Exception as e:
            logger.error(f"❌ 群B Bot配置失败: {e}")
            # 使用最简单的配置
            bot_b = telegram.Bot(token=TELEGRAM_TOKEN)
            logger.info("🌐 群B使用默认Bot配置")

        # 发送消息到群B
        logger.info("📤 正在发送消息到群B...")
        message_obj = bot_b.send_message(
            chat_id=TELEGRAM_CHAT_ID_GROUP_B,
            text=cleaned_message,
            parse_mode='Markdown'
        )
        logger.info(f"✅ 消息发送到群B成功！消息ID: {message_obj.message_id}")
        return True

    except Exception as e:
        logger.error(f"❌ 发送Telegram消息到群B失败: {e}")
        return False

async def send_to_telegram(message, token_address=None):
    """发送消息到Telegram - 修复版本"""
    try:
        # 检查telegram模块是否可用
        try:
            import telegram
            logger.info("✅ Telegram模块导入成功")
        except ImportError as import_error:
            logger.error(f"❌ Telegram 模块不可用，无法发送消息到 Telegram: {import_error}")
            return False

        logger.info("🚀 开始发送消息到Telegram...")
        logger.info(f"📱 目标群组ID: {TELEGRAM_CHAT_ID}")
        logger.info(f"🤖 Bot Token: {TELEGRAM_TOKEN[:10]}...")
        logger.info(f"📝 消息长度: {len(message)} 字符")

        # 清理消息中的问题字符
        try:
            # 移除代理字符和其他问题字符
            cleaned_message = message.encode('utf-8', errors='ignore').decode('utf-8')
            # 移除零宽字符和其他特殊字符
            import re
            cleaned_message = re.sub(r'[\u200d\ufeff\u200b-\u200f\u2060-\u206f]', '', cleaned_message)
            logger.info("✅ 消息编码检查通过")
        except Exception as encoding_error:
            logger.error(f"❌ 消息编码处理失败: {encoding_error}")
            return False

        # 根据代理开关设置请求配置
        try:
            # 尝试不同版本的导入方式
            try:
                from telegram.request import HTTPXRequest
            except ImportError:
                try:
                    from telegram.ext import HTTPXRequest
                except ImportError:
                    # 如果都导入失败，使用简单的Bot初始化
                    HTTPXRequest = None

            if USE_PROXY:
                # 尝试多种代理配置方式
                try:
                    if HTTPXRequest:
                        # 方式1: 使用proxy_url参数
                        try:
                            request = HTTPXRequest(
                                connection_pool_size=8,
                                proxy_url=PROXY_URL
                            )
                            bot = telegram.Bot(token=TELEGRAM_TOKEN, request=request)
                            logger.info(f"🌐 使用代理发送(proxy_url): {PROXY_URL}")
                        except Exception as e1:
                            logger.warning(f"proxy_url方式失败: {e1}")
                            # 方式2: 使用proxy参数
                            try:
                                request = HTTPXRequest(
                                    connection_pool_size=8,
                                    proxy=PROXY_URL
                                )
                                bot = telegram.Bot(token=TELEGRAM_TOKEN, request=request)
                                logger.info(f"🌐 使用代理发送(proxy): {PROXY_URL}")
                            except Exception as e2:
                                logger.warning(f"proxy方式失败: {e2}")
                                # 方式3: 简单Bot + 环境变量代理
                                import os
                                os.environ['HTTP_PROXY'] = PROXY_URL
                                os.environ['HTTPS_PROXY'] = PROXY_URL
                                bot = telegram.Bot(token=TELEGRAM_TOKEN)
                                logger.info(f"🌐 使用环境变量代理: {PROXY_URL}")
                    else:
                        # 如果HTTPXRequest不可用，使用环境变量
                        import os
                        os.environ['HTTP_PROXY'] = PROXY_URL
                        os.environ['HTTPS_PROXY'] = PROXY_URL
                        bot = telegram.Bot(token=TELEGRAM_TOKEN)
                        logger.info(f"🌐 使用环境变量代理: {PROXY_URL}")
                except Exception as e:
                    logger.error(f"代理配置失败: {e}")
                    raise
            else:
                # 直连配置
                if HTTPXRequest:
                    request = HTTPXRequest(connection_pool_size=8)
                    bot = telegram.Bot(token=TELEGRAM_TOKEN, request=request)
                    logger.info("🌐 直连发送（无代理）")
                else:
                    bot = telegram.Bot(token=TELEGRAM_TOKEN)
                    logger.info("🌐 使用简单Bot配置")
        except Exception as e:
            logger.error(f"❌ Bot配置失败: {e}")
            # 使用最简单的配置
            bot = telegram.Bot(token=TELEGRAM_TOKEN)
            logger.info("🌐 使用默认Bot配置")

        # 发送消息
        logger.info("📤 正在发送消息...")
        message_obj = bot.send_message(
            chat_id=TELEGRAM_CHAT_ID,
            text=cleaned_message,
            parse_mode='Markdown'
        )
        logger.info(f"✅ 消息发送成功！消息ID: {message_obj.message_id}")
        return True

    except Exception as e:
        logger.error(f"❌ 发送Telegram消息失败: {e}")
        return False

def calculate_time_diff(timestamp_str, detection_time_str):
    """计算两个时间字符串之间的分钟差"""
    if timestamp_str == "null" or detection_time_str == "null":
        return None

    try:
        # 将字符串转换为时间对象
        timestamp = datetime.strptime(timestamp_str, "%Y-%m-%d %H:%M:%S")
        detection_time = datetime.strptime(detection_time_str, "%Y-%m-%d %H:%M:%S")

        # 计算时间差（秒）
        diff_seconds = (detection_time - timestamp).total_seconds()

        # 转换为分钟
        diff_minutes = int(diff_seconds / 60)

        return diff_minutes
    except Exception as e:
        logger.error(f"计算时间差时出错: {e}")
        return None


async def search_channel_messages(channel_id, token_address):
    """搜索指定频道中包含代币地址的消息"""
    try:
        # 获取频道实体 - 修复实体获取方式
        channel = await telegram_client.get_entity(channel_id)
        # 安全获取频道名称
        channel_name = getattr(channel, 'title', f'Channel_{channel_id}')

        logger.info(f"开始搜索频道 '{channel_name}' 中包含代币地址 '{token_address}' 的消息")

        # 搜索消息
        messages = []

        # 尝试使用Telegram的搜索功能
        try:
            logger.info(f"使用Telegram搜索功能查找关键词 '{token_address}'")
            async for message in telegram_client.iter_messages(
                channel_id,
                search=token_address,
                limit=16
            ):
                if message and message.text:
                    logger.info(f"在频道 '{channel_name}' 中找到包含代币地址的消息: {message.id}")
                    messages.append({
                        'channel_id': channel_id,
                        'channel_name': channel_name,
                        'message_id': message.id,
                        'date': message.date,
                        'text': message.text
                    })
            logger.info(f"Telegram搜索功能找到 {len(messages)} 条消息")
        except Exception as search_error:
            logger.warning(f"使用Telegram搜索功能时出错: {search_error}")

        # 如果搜索功能没有找到消息，使用手动搜索
        if not messages:
            logger.info(f"开始手动搜索频道 '{channel_name}' 的最新消息")
            message_count = 0
            async for message in telegram_client.iter_messages(channel_id, limit=0):
                message_count += 1
                if message_count % 100 == 0:
                    logger.info(f"已检查 {message_count} 条消息")

                # 检查消息是否包含代币地址
                if message and message.text:
                    text = message.text.lower()
                    address = token_address.lower()

                    # 各种匹配方式
                    if (address in text or
                        f"`{address}`" in text or
                        f"'{address}'" in text or
                        f'"{address}"' in text or
                        f"[{address}]" in text or
                        f"({address})" in text or
                        f"/{address}" in text or
                        f"={address}" in text or
                        f"ca: {address}" in text or
                        f"ca:{address}" in text or
                        f"ca: {address} " in text or
                        ("ca: [" in text and address in text)):

                        logger.info(f"在频道 '{channel_name}' 中找到包含代币地址的消息: {message.id}")
                        messages.append({
                            'channel_id': channel_id,
                            'channel_name': channel_name,
                            'message_id': message.id,
                            'date': message.date,
                            'text': message.text
                        })
                        continue

            logger.info(f"手动搜索共检查了 {message_count} 条消息")

        return messages

    except Exception as e:
        logger.error(f"搜索频道 {channel_id} 时出错: {e}")
        return []

async def search_all_channels(token_address):
    """在所有目标频道中搜索代币地址"""
    try:
        logger.info(f"开始在 {len(TARGET_CHANNELS)} 个频道中搜索代币地址: {token_address}")

        # 创建任务列表，并发搜索所有频道
        tasks = [search_channel_messages(channel_id, token_address) for channel_id in TARGET_CHANNELS]
        search_results = await asyncio.gather(*tasks)

        # 展平结果列表
        all_results = []
        for channel_results in search_results:
            all_results.extend(channel_results)

        # 按时间排序，最新的消息在前
        all_results.sort(key=lambda x: x['date'], reverse=True)

        logger.info(f"搜索完成，共找到 {len(all_results)} 条相关消息")
        return all_results

    except Exception as e:
        logger.error(f"搜索代币地址时出错: {e}")
        return []


def extract_info_from_pumpfun(message_text):
    """从Pump.fun频道的消息中提取信息"""
    info = {}

    # 直接使用当前有效的匹配格式
    # 匹配格式如: ├ [17.1%] | [16.1%](...) | ...

    return info


def extract_info_from_gmgn(message_text):
    """从GMGN频道的消息中提取信息"""
    info = {}

    # 匹配格式如: 📕 Rug Probability跑路概率: **100%**(**39/39**)
    rug_match = re.search(r'Rug Probability跑路概率:\s*\*\*([\d.]+%)\*\*\(\*\*([\d/]+)\*\*\)', message_text)
    if rug_match:
        info['rug_probability'] = f"{rug_match.group(1)}({rug_match.group(2)})"

    return info


def extract_info_from_hunter(message_text):
    """从Pump内盘Alert-猎人频道的消息中提取信息"""
    # 直接使用当前有效的匹配格式
    # 提取从DEV开始到🐦twitter之前的内容
    dev_start = message_text.find("💻DEV:")
    twitter_pos = message_text.find("🐦twitter", dev_start)
    if twitter_pos == -1:
        twitter_pos = message_text.find("🐦[twitter]", dev_start)

    # 如果找到了DEV信息的开始和twitter标记
    if dev_start != -1 and twitter_pos != -1 and twitter_pos > dev_start:
        # 提取从DEV开始到twitter标记之前的内容
        hunter_content = message_text[dev_start:twitter_pos].strip()

        # 移除隐藏的URL链接
        # 匹配格式如: [wVtA](https://gmgn.ai/sol/address/FzQ26uBAmzyqxKLD3uBTn5vkbptqJSMkbGXF6FJCwVtA)
        hunter_content = re.sub(r'\[([^\]]+)\]\(https?://[^\)]+\)', r'\1', hunter_content)

        return {'hunter_content': hunter_content}

    return {}


async def extract_telegram_info(token_address):
    """从Telegram频道中提取代币信息 - 使用已验证的连接配置"""
    # 确保客户端已连接
    if not telegram_client.is_connected():
        await telegram_client.connect()

    # 检查是否已经授权
    if not await telegram_client.is_user_authorized():
        logger.warning("Telegram客户端未授权，无法搜索消息")
        return {}

    # 搜索所有频道
    results = await search_all_channels(token_address)

    # 初始化提取的信息字典
    extracted_info = {}

    # 从每个频道的消息中提取信息
    for result in results:
        channel_id = result['channel_id']
        message_text = result['text']
        channel_type = CHANNEL_TYPE_MAP.get(channel_id)

        # 根据频道类型选择不同的提取方法
        if channel_type == 'pumpfun':
            pumpfun_info = extract_info_from_pumpfun(message_text)
            # 只有当成功提取到信息时才更新
            if pumpfun_info:
                extracted_info.update(pumpfun_info)
        elif channel_type == 'gmgn':
            gmgn_info = extract_info_from_gmgn(message_text)
            if gmgn_info:
                extracted_info.update(gmgn_info)
        elif channel_type == 'hunter':
            hunter_info = extract_info_from_hunter(message_text)
            if hunter_info:
                # 添加频道名称信息
                hunter_info['hunter_channel_name'] = result['channel_name']
                extracted_info.update(hunter_info)

    # 添加原始消息数量
    extracted_info['message_count'] = len(results)
    extracted_info['messages'] = results

    return extracted_info


def format_token_info(token_info):
    """格式化代币信息为Markdown格式"""
    # 安全处理函数，确保文本安全用于Markdown
    def safe_text(text):
        if text is None:
            return "null"
        # 将文本转换为字符串
        text = str(text)
        # 转义Markdown特殊字符
        for char in ['_', '*', '`', '[', ']']:
            text = text.replace(char, f"\\{char}")
        return text

    message = f"🔔 *新代币检测* 🔔\n\n"
    message += f"🪙 *代币地址*: `{token_info.get('token_address', 'null')}`\n"
    message += f"📝 *名称*: {safe_text(token_info.get('name', 'null'))}\n"
    message += f"🔤 *符号*: {safe_text(token_info.get('symbol', 'null'))}\n"
    message += f"📋 *描述*: {safe_text(token_info.get('description', 'null'))}\n\n"

    message += f"👤 *创建者*: `{token_info.get('creator', 'null')}`\n"
    # 获取检测时间
    detection_time = safe_text(token_info.get('timestamp', 'null'))

    # 创建时间
    created_timestamp = safe_text(token_info.get('created_timestamp', 'null'))
    created_diff = calculate_time_diff(created_timestamp, detection_time)
    if created_diff is not None and created_timestamp != "null":
        message += f"⏰ *创建时间*: {created_diff}分钟. {created_timestamp}\n\n"
    else:
        message += f"⏰ *创建时间*: {created_timestamp}\n\n"

    # SolanaTracker新增字段
    message += f"📊 *SolanaTracker风险数据* 📊\n"
    message += f"🎯 *狙击数*: {safe_text(token_info.get('snipers_count', 'null'))}\n"
    message += f"📈 *狙击占比*: {safe_text(token_info.get('snipers_percentage', 'null'))}%\n"
    message += f"🔍 *内幕数*: {safe_text(token_info.get('insiders_count', 'null'))}\n"
    message += f"📊 *内幕占比*: {safe_text(token_info.get('insiders_percentage', 'null'))}%\n"
    message += f"🏆 *top 10占比*: {safe_text(token_info.get('top10_percentage', 'null'))}%\n"
    message += f"👥 *holder人数*: {safe_text(token_info.get('holders_count', 'null'))}\n"
    message += f"📈 *1m Change*: {safe_text(token_info.get('price_change_1m', 'null'))}%\n"
    message += f"📈 *5m Change*: {safe_text(token_info.get('price_change_5m', 'null'))}%\n\n"

    # Axiom指标值 - 使用新的API字段
    message += f"\ud83d\udcca *Axiom指标* \ud83d\udcca\n"
    message += f"\ud83d\udcc9 *开发者代币总数*: {safe_text(token_info.get('axiom_totalCount', 'null'))}\n"
    message += f"\ud83d\udd04 *已迁移代币数*: {safe_text(token_info.get('axiom_migratedCount', 'null'))}\n"
    message += f"\ud83d\udc65 *Axiom持有者数量*: {safe_text(token_info.get('axiom_numHolders', 'null'))}\n"
    message += f"\ud83e\udd16 *机器人用户数*: {safe_text(token_info.get('axiom_numBotUsers', 'null'))}\n"
    message += f"\ud83d\udcc9 *前10大持有者占比*: {safe_text(token_info.get('axiom_top10HoldersPercent', 'null'))}%\n"
    message += f"\ud83d\udc68\u200d\ud83d\udcbb *开发者持有占比*: {safe_text(token_info.get('axiom_devHoldsPercent', 'null'))}%\n"
    message += f"\ud83d\udd10 *内部人士持有占比*: {safe_text(token_info.get('axiom_insidersHoldPercent', 'null'))}%\n"
    message += f"\ud83d\udcb9 *打包者持有占比*: {safe_text(token_info.get('axiom_bundlersHoldPercent', 'null'))}%\n"
    message += f"\ud83c\udfa3 *狙击手持有占比*: {safe_text(token_info.get('axiom_snipersHoldPercent', 'null'))}%\n"
    message += f"\ud83d\udcb0 *总配对费用*: {safe_text(token_info.get('axiom_totalPairFeesPaid', 'null'))}\n\n"

    # Ave.ai 指标值
    message += f"🔗 *Ave.ai标签统计* 🔗\n"
    message += f"  Cabal /阴谋集团: {safe_text(token_info.get('ave_cabal', 'null'))}\n"
    message += f"  Phishing /钓鱼地址: {safe_text(token_info.get('ave_phishing', 'null'))}\n"
    message += f"  Insiders /老鼠仓: {safe_text(token_info.get('ave_insiders', 'null'))}\n"
    message += f"  Bundle /捆绑地址: {safe_text(token_info.get('ave_bundle', 'null'))}\n"

    message += f"  Snipers /狙击: {safe_text(token_info.get('ave_snipers', 'null'))}\n"
    message += f"  DEV /DEV: {safe_text(token_info.get('ave_dev', 'null'))}\n"
    message += f"  Whale /巨鲸: {safe_text(token_info.get('ave_whale', 'null'))}\n"
    message += f"  Smarters /聪明钱: {safe_text(token_info.get('ave_smarters', 'null'))}\n"
    message += f"  KOL /KOL: {safe_text(token_info.get('ave_kol', 'null'))}\n\n"

    message += f"  Rug Pull统计: {safe_text(token_info.get('ave_rug_pull_stats', 'null'))}\n"
    message += f"    Insiders: {safe_text(token_info.get('ave_insiders_rate', 'null'))}\n"
    message += f"    Phishing: {safe_text(token_info.get('ave_phishing_rate', 'null'))}\n"
    message += f"    Cabal: {safe_text(token_info.get('ave_cabal_rate', 'null'))}\n"
    message += f"    Bundle: {safe_text(token_info.get('ave_bundle_rate', 'null'))}\n"
    message += f"    All Tag Rate: {safe_text(token_info.get('ave_all_tag_rate', 'null'))}\n\n"

    """ message += f"  价格变化 /5分钟: {safe_text(token_info.get('ave_price_change_5m', 'null'))}\n"
     message += f"  买入量 /5分钟: {safe_text(token_info.get('ave_buy_volume_5m', 'null'))}\n"
    message += f"  卖出量 /5分钟: {safe_text(token_info.get('ave_sell_volume_5m', 'null'))}\n"
    message += f"  总交易量 /5分钟: {safe_text(token_info.get('ave_total_volume_5m', 'null'))}\n"
    message += f"  买入交易数 /5分钟: {safe_text(token_info.get('ave_buy_tx_5m', 'null'))}\n"
    message += f"  卖出交易数 /5分钟: {safe_text(token_info.get('ave_sell_tx_5m', 'null'))}\n"
    message += f"  买家数量 /5分钟: {safe_text(token_info.get('ave_buyers_5m', 'null'))}\n"
    message += f"  卖家数量 /5分钟: {safe_text(token_info.get('ave_sellers_5m', 'null'))}\n\n"
    message += f"  聪明钱买入次数 /24小时: {safe_text(token_info.get('ave_smart_buy_24h', 'null'))}\n"
    message += f"  聪明钱卖出次数 /24小时: {safe_text(token_info.get('ave_smart_sell_24h', 'null'))}\n\n" """

    # GMGN 指标值
    message += f"🔍 *GMGN统计数据* 🔍\n"
    message += f"👥 *GMGN持有者数量*: {safe_text(token_info.get('gmgn_holder_count', 'null'))}\n"
    message += f"💎 *蓝筹持有者占比*: {safe_text(token_info.get('gmgn_bluechip_owner_percentage', 'null'))}\n"
    message += f"📢 *信号数量*: {safe_text(token_info.get('gmgn_signal_count', 'null'))}\n"
    message += f"🎯 *Degen呼叫数*: {safe_text(token_info.get('gmgn_degen_call_count', 'null'))}\n"
    message += f"🐀 *顶级老鼠交易者占比*: {safe_text(token_info.get('gmgn_top_rat_trader_percentage', 'null'))}\n"
    message += f"📦 *顶级打包者占比*: {safe_text(token_info.get('gmgn_top_bundler_trader_percentage', 'null'))}\n"
    message += f"🕳️ *顶级陷阱交易者占比*: {safe_text(token_info.get('gmgn_top_entrapment_trader_percentage', 'null'))}\n\n"

    message += f"💰 *平均持有余额*: {safe_text(token_info.get('gmgn_avg_holding_balance', 'null'))}\n"
    message += f"🔟 *GMGN前10持有者占比*: {safe_text(token_info.get('gmgn_top10_holder_percent', 'null'))}\n"
    message += f"💯 *前100持有者占比*: {safe_text(token_info.get('gmgn_top100_holder_percent', 'null'))}\n\n"

    message += f"🧠 *智能钱包*: {safe_text(token_info.get('gmgn_smart_wallets', 'null'))}\n"
    message += f"🆕 *新钱包*: {safe_text(token_info.get('gmgn_fresh_wallets', 'null'))}\n"
    message += f"⭐ *知名钱包*: {safe_text(token_info.get('gmgn_renowned_wallets', 'null'))}\n"
    message += f"👨‍💻 *创建者钱包*: {safe_text(token_info.get('gmgn_creator_wallets', 'null'))}\n"
    message += f"🎯 *狙击手钱包*: {safe_text(token_info.get('gmgn_sniper_wallets', 'null'))}\n"
    message += f"🐀 *老鼠交易者钱包*: {safe_text(token_info.get('gmgn_rat_trader_wallets', 'null'))}\n"
    message += f"🐋 *鲸鱼钱包*: {safe_text(token_info.get('gmgn_whale_wallets', 'null'))}\n"
    message += f"🔝 *顶级钱包*: {safe_text(token_info.get('gmgn_top_wallets', 'null'))}\n"
    message += f"👥 *关注钱包*: {safe_text(token_info.get('gmgn_following_wallets', 'null'))}\n"
    message += f"📦 *打包者钱包*: {safe_text(token_info.get('gmgn_bundler_wallets', 'null'))}\n\n"

    # Twitter变更统计
    message += f"🐦 *Twitter变更次数*: {safe_text(token_info.get('gmgn_twitter_change', 'null'))}\n"
    message += f"🐦 *Twitter删除帖子代币数*: {safe_text(token_info.get('gmgn_twitter_del_post_token_count', 'null'))}\n"
    message += f"🐦 *Twitter创建代币数*: {safe_text(token_info.get('gmgn_twitter_create_token_count', 'null'))}\n"

    # 开发者相关数据
    message += f"👨‍💻 *创建者代币余额*: {safe_text(token_info.get('gmgn_creator_token_balance', 'null'))}\n"
    message += f"📢 *DEX广告状态*: {safe_text(token_info.get('gmgn_dexscr_ad', 'null'))}\n"
    message += f"🏷️ *CTO标志*: {safe_text(token_info.get('gmgn_cto_flag', 'null'))}\n"

    # 价格数据
    message += f"💲 *当前价格*: {safe_text(token_info.get('gmgn_price', 'null'))}\n"
    message += f"💲 *1分钟价格*: {safe_text(token_info.get('gmgn_price_1m', 'null'))}\n"

    # 1分钟交易数据
    message += f"📊 *1分钟买入次数*: {safe_text(token_info.get('gmgn_buys_1m', 'null'))}\n"
    message += f"📊 *1分钟卖出次数*: {safe_text(token_info.get('gmgn_sells_1m', 'null'))}\n"
    message += f"💹 *1分钟交易量*: {safe_text(token_info.get('gmgn_volume_1m', 'null'))}\n"
    message += f"🟢 *1分钟买入量*: {safe_text(token_info.get('gmgn_buy_volume_1m', 'null'))}\n"
    message += f"🔴 *1分钟卖出量*: {safe_text(token_info.get('gmgn_sell_volume_1m', 'null'))}\n"

    # 5分钟交易数据
    message += f"📈 *5分钟买入次数*: {safe_text(token_info.get('gmgn_buys_5m', 'null'))}\n"
    message += f"📉 *5分钟卖出次数*: {safe_text(token_info.get('gmgn_sells_5m', 'null'))}\n"
    message += f"💹 *5分钟交易量*: {safe_text(token_info.get('gmgn_volume_5m', 'null'))}\n"
    message += f"🟢 *5分钟买入量*: {safe_text(token_info.get('gmgn_buy_volume_5m', 'null'))}\n"
    message += f"🔴 *5分钟卖出量*: {safe_text(token_info.get('gmgn_sell_volume_5m', 'null'))}\n"

    # 热度等级
    message += f"🔥 *热度等级*: {safe_text(token_info.get('gmgn_hot_level', 'null'))}\n\n"

    message += f"    ✅ *hold*: {safe_text(token_info.get('gmgn_top_buyers_hold', 'null'))}\n"
    message += f"    ✅ *bought_more*: {safe_text(token_info.get('gmgn_top_buyers_bought_more', 'null'))}\n"
    message += f"    ✅ *transfered*: {safe_text(token_info.get('gmgn_top_buyers_transfered', 'null'))}\n"
    message += f"    ✅ *bought_rate*: {safe_text(token_info.get('gmgn_top_buyers_bought_rate', 'null'))}\n"
    message += f"    ✅ *holding_rate*: {safe_text(token_info.get('gmgn_top_buyers_holding_rate', 'null'))}\n\n"

    message += f"    📋 *符合条件的钱包数量*: {safe_text(token_info.get('gmgn_filtered_holders_count', 'null'))}\n"
    message += f"    🏆 *最大持有者占比*: {safe_text(token_info.get('gmgn_biggest_holder', 'null'))}\n"
    message += f"    ✅ *native_balance >= 6000000000 的个数*: {safe_text(token_info.get('gmgn_native_balance_ge_6b', 'null'))}\n"

    # native_transfer.name 统计
    message += f"    ✅ *native_transfer.name 统计*:\n"
    # 动态获取所有 native_transfer 相关字段
    native_transfer_fields = {k: v for k, v in token_info.items() if k.startswith('gmgn_native_transfer_') and v != 'null'}
    if native_transfer_fields:
        for field_name, count in native_transfer_fields.items():
            # 从字段名中提取原始名称
            original_name = field_name.replace('gmgn_native_transfer_', '').replace('_', ' ')
            if original_name == 'null':
                original_name = 'null'
            message += f"      • *{original_name}*: {count}\n"
    else:
        message += f"      • *无数据*\n"

    message += f"    ✅ *tags 中包含 photon 的次数*: {safe_text(token_info.get('gmgn_tags_photon_count', 'null'))}\n"

    # maker_token_tags 统计
    message += f"    ✅ *maker_token_tags 统计*:\n"
    # 动态获取所有 maker_token_tag 相关字段
    maker_token_fields = {k: v for k, v in token_info.items() if k.startswith('gmgn_maker_token_tag_') and v != 'null'}
    if maker_token_fields:
        for field_name, count in maker_token_fields.items():
            # 从字段名中提取原始标签名
            original_tag = field_name.replace('gmgn_maker_token_tag_', '').replace('_', ' ')
            message += f"      • *{original_tag}*: {count}\n"
    else:
        message += f"      • *无数据*\n"

    message += f"    ✅ *transfer_in: true 出现次数*: {safe_text(token_info.get('gmgn_transfer_in_true', 'null'))}\n"
    message += f"    ✅ *is_new: true 出现次数*: {safe_text(token_info.get('gmgn_is_new_true', 'null'))}\n"
    message += f"    ✅ *is_suspicious: true 出现次数*: {safe_text(token_info.get('gmgn_is_suspicious_true', 'null'))}\n"
    message += f"    ✅ *sandwich_bot 出现次数*: {safe_text(token_info.get('gmgn_sandwich_bot_count', 'null'))}\n"
    message += f"    ✅ *bundler 出现次数*: {safe_text(token_info.get('gmgn_bundler_count', 'null'))}\n\n"

    message += f"🌐 *网站*: {safe_text(token_info.get('website', 'null'))}\n"
    message += f"🐦 *Twitter*: {safe_text(token_info.get('twitter', 'null'))}\n"
    message += f"📱 *Telegram*: {safe_text(token_info.get('telegram', 'null'))}\n"
  #  message += f"🔍 *DexScreener Twitter*: {safe_text(token_info.get('dexscreener_twitter', 'null'))}\n\n"

    # message += f"🔐 *DexPaid状态*: {safe_text(token_info.get('dexpaid_status', 'null'))}\n"


    # DexPaid支付时间
    dexpaid_diff_minutes = safe_text(token_info.get('dexpaid_payment_time_diff_minutes', 'null'))
    dexpaid_timestamp = safe_text(token_info.get('dexpaid_paymentTimestamp', 'null'))

    if dexpaid_diff_minutes != "null" and dexpaid_timestamp != "null":
        message += f"💲 *DexPaid支付时间*: {dexpaid_diff_minutes}分钟. {dexpaid_timestamp}\n\n"
    elif dexpaid_timestamp != "null":
        message += f"💲 *DexPaid支付时间*: {dexpaid_timestamp}\n\n"
    else:
        message += f"💲 *DexPaid支付时间*: null\n\n"



    # Telegram频道信息
    message += f"\ud83d\udcac *Telegram频道信息* \ud83d\udcac\n"
    message += f"🚨 *跑路概率*: {safe_text(token_info.get('telegram_rug_probability', 'null'))}\n"

    # 猎人频道信息
    hunter_content = token_info.get('telegram_hunter_content', 'null')
    hunter_channel_name = token_info.get('hunter_channel_name', 'null')

    # 显示猎人频道信息标题
    message += f"\n\ud83d\udc68\u200d\ud83d\udcbb *猎人频道信息*: "

    if hunter_content != 'null':
        # 如果有频道名称信息，添加到内容中
        if hunter_channel_name != 'null':
            message += f"\n【来源: {safe_text(hunter_channel_name)}】\n{safe_text(hunter_content)}\n"
        else:
            message += f"\n{safe_text(hunter_content)}\n"
    else:
        message += "null\n"

    message += f"\ud83d\udcac *相关消息数量*: {safe_text(token_info.get('telegram_message_count', '0'))}\n\n"

    message += f"⏱️ *检测时间*: {safe_text(token_info.get('timestamp', 'null'))}"

    return message

async def subscribe_new_tokens():
    """订阅PumpPortal的新代币事件"""
    uri = "wss://pumpportal.fun/api/data"

    while True:
        try:
            logger.info("正在连接到PumpPortal WebSocket...")
            async with websockets.connect(uri) as websocket:
                # 订阅新代币创建事件
                payload = {
                    "method": "subscribeMigration",
                }
                await websocket.send(json.dumps(payload))
                logger.info("已订阅Migration事件")

                async for message in websocket:
                    try:
                        data = json.loads(message)
                        logger.info(f"收到新消息: {data}")

                        # 检查是否是新代币事件
                        if "mint" in data:
                            token_address = data.get("mint")
                            logger.info(f"检测到新代币: {token_address}")

                            # 延迟2.6秒后再继续处理
                            logger.info(f"延迟2.6秒后开始处理代币: {token_address}")
                            await asyncio.sleep(2.6)

                            # 处理代币信息
                            token_info = await process_token(token_address)

                            # 格式化并发送到Telegram群A
                            formatted_message = format_token_info(token_info)
                            success = await send_to_telegram(formatted_message, token_address)
                            if not success:
                                logger.warning("发送到Telegram群A失败，将在日志中记录消息")
                                logger.info(f"消息内容:\n{formatted_message}")
                            else:
                                logger.info("✅ 消息已成功发送到群A")

                                # 功能1：检查过滤条件并发送到群B
                                try:
                                    filter_passed, filter_reason = check_filter_conditions(token_info)
                                    if filter_passed:
                                        logger.info("🎯 代币满足过滤条件，开始发送到群B...")
                                        success_b = await send_to_telegram_group_b(formatted_message, token_address)
                                        if success_b:
                                            logger.info("✅ 消息已成功发送到群B")
                                            # 只有成功发送到群B的代币地址才写入交易信号文件
                                            await write_to_trading_bot_signal_file(token_address)
                                        else:
                                            logger.warning("⚠️ 发送到群B失败")
                                    else:
                                        logger.info(f"❌ 代币未通过过滤，不发送到群B - 原因: {filter_reason}")
                                except Exception as filter_error:
                                    logger.error(f"❌ 功能1过滤处理出错: {filter_error}")
                    except json.JSONDecodeError:
                        logger.error(f"无法解析消息: {message}")
                    except Exception as e:
                        logger.error(f"处理消息时出错: {e}")

        except Exception as e:
            logger.error(f"WebSocket连接出错: {e}")
            logger.info("3秒后重新连接...")
            await asyncio.sleep(1.6)



async def init_telegram_client():
    """初始化Telegram客户端"""
    try:
        # 连接到Telegram
        await telegram_client.connect()

        # 检查是否已经授权
        if not await telegram_client.is_user_authorized():
            logger.info("需要登录Telegram账户")

            # 发送验证码
            await telegram_client.send_code_request(PHONE_NUMBER)

            try:
                # 请求用户输入验证码
                verification_code = input("请输入收到的验证码: ")
                await telegram_client.sign_in(PHONE_NUMBER, verification_code)
            except SessionPasswordNeededError:
                # 如果启用了两步验证，需要输入密码
                password = input("请输入两步验证密码: ")
                await telegram_client.sign_in(password=password)

        logger.info("已成功登录Telegram")
        return True
    except Exception as e:
        logger.error(f"Telegram客户端初始化失败: {e}")
        return False

async def periodic_status_monitor():
    """定期监控GMGN连接池状态"""
    while True:
        try:
            await asyncio.sleep(1800)  # 每30分钟检查一次

            # 获取GMGN客户端并记录状态
            gmgn_client = get_gmgn_client()
            gmgn_client.log_pool_status()  # 定期监控时显示状态

            # 记录运行时间
            current_time = time.time()
            if hasattr(periodic_status_monitor, 'start_time'):
                runtime_hours = (current_time - periodic_status_monitor.start_time) / 3600
                logger.info(f"⏰ 程序已运行: {runtime_hours:.1f} 小时")
            else:
                periodic_status_monitor.start_time = current_time

        except Exception as e:
            logger.error(f"状态监控出错: {e}")

async def main():
    """主函数 - 订阅PumpPortal的新代币事件"""
    logger.info("启动代币数据聚合器...")
    logger.info("🔄 GMGN连接池轮换机制已启用")

    # 初始化Telegram客户端
    telegram_initialized = await init_telegram_client()
    if not telegram_initialized:
        logger.error("Telegram客户端初始化失败，程序将继续运行，但不会搜索Telegram频道")

    # 启动状态监控任务
    status_monitor_task = asyncio.create_task(periodic_status_monitor())
    logger.info("📊 状态监控任务已启动")

    try:
        # 运行WebSocket客户端，订阅新代币事件
        logger.info("开始订阅PumpPortal新代币事件...")
        await subscribe_new_tokens()
    finally:
        # 取消状态监控任务
        status_monitor_task.cancel()
        try:
            await status_monitor_task
        except asyncio.CancelledError:
            pass

if __name__ == "__main__":
    # 运行主函数
    asyncio.run(main())