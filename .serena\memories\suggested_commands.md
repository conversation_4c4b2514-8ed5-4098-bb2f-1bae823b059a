# 建议的命令

## 运行项目
```bash
# 主程序运行
python main.py

# 带实例ID运行（支持多实例）
python main.py instance_1
```

## 开发和调试
```bash
# 查看日志文件
type token_aggregator.log

# 实时查看日志
Get-Content token_aggregator.log -Wait

# 检查配置文件
type filter_config.txt
```

## Windows系统命令
```bash
# 文件操作
dir                    # 列出文件
type filename          # 查看文件内容
copy source dest       # 复制文件
del filename           # 删除文件

# 进程管理
tasklist               # 查看进程
taskkill /PID xxxx     # 终止进程

# 网络检查
ping google.com        # 网络连通性测试
netstat -an           # 查看网络连接
```

## Git操作
```bash
git status            # 查看状态
git add .             # 添加所有更改
git commit -m "message"  # 提交更改
git push              # 推送到远程
git pull              # 拉取更新
```

## Python环境
```bash
# 安装依赖
pip install -r requirements.txt

# 检查Python版本
python --version

# 检查已安装包
pip list
```