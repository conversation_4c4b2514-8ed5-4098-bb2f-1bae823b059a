# 代码风格和约定

## 编码规范
- **编码**: UTF-8
- **缩进**: 4个空格
- **行长度**: 无严格限制，但保持可读性

## 命名约定
- **变量名**: snake_case（如：token_address, field_mapping）
- **函数名**: snake_case（如：check_filter_conditions, process_token）
- **类名**: PascalCase（如：AxiomAPIClient, GmgnApiClient）
- **常量**: UPPER_CASE（如：TELEGRAM_TOKEN, PROXY_URL）

## 日志规范
- 使用自定义的SafeFormatter处理Unicode字符
- 日志级别：INFO用于重要信息，DEBUG用于调试，ERROR用于错误
- 日志格式：时间戳 - 模块名 - 级别 - 消息
- 同时输出到控制台和文件

## 异常处理
- 使用try-except包装可能出错的代码
- 记录详细的错误信息到日志
- 对于网络请求，设置合理的超时时间

## 异步编程
- 使用async/await进行异步操作
- 使用asyncio.gather进行并发处理
- 合理使用asyncio.sleep进行延迟

## 配置管理
- 配置参数定义在文件顶部
- 支持代理开关配置
- 使用配置文件进行过滤条件管理