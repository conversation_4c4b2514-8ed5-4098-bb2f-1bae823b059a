# 代币信号文件写入功能实现总结

## 项目背景

本文档记录了在pumpswap-signal筛选脚本中实现"将代币地址写入token_signals.txt文件"功能的完整过程。该功能用于将筛选出的代币地址传递给策略交易脚本，实现两个系统间的数据对接。

### 系统架构
- **外部筛选脚本**: `D:\pumpswap-signal\main.py`
- **策略交易脚本**: `D:\meme trade bot\`
- **信号文件**: `D:\meme trade bot\token_signals.txt`

## 1. 实现步骤回顾

### 1.1 需求分析
**目标**: 当筛选脚本成功发送代币信息到Telegram群时，同时将代币地址写入交易脚本的信号文件。

**要求**:
- 实时写入代币地址
- 支持多实例并发运行
- 确保文件格式正确（每行一个地址）
- 提供错误处理和日志记录

### 1.2 方案选择
选择了**共享文件对接方案**，原因：
- ✅ 最简单直接，无需修改现有代码架构
- ✅ 实时性好，文件变化立即触发处理
- ✅ 稳定可靠，基于文件系统的成熟机制
- ✅ 支持批量信号处理

### 1.3 具体实现步骤

#### 步骤1: 创建写入函数
```python
async def write_to_trading_bot_signal_file(token_address):
    """写入代币地址到交易机器人的信号文件"""
    try:
        signal_file_path = r"D:\meme trade bot\token_signals.txt"
        
        # 文件锁定和写入逻辑
        with open(signal_file_path, 'a', encoding='utf-8') as f:
            try:
                import msvcrt
                msvcrt.locking(f.fileno(), msvcrt.LK_LOCK, 1)
                f.write(f"{token_address}\n")
                f.flush()
                msvcrt.locking(f.fileno(), msvcrt.LK_UNLCK, 1)
            except ImportError:
                f.write(f"{token_address}\n")
                f.flush()
        
        logger.info(f"✅ 已写入交易信号文件: {token_address}")
        return True
        
    except Exception as e:
        logger.error(f"❌ 写入交易信号文件失败: {e}")
        return False
```

#### 步骤2: 修改Telegram发送函数
```python
async def send_to_telegram(message, token_address=None):
    # 原有发送逻辑...
    
    # 发送成功后的处理
    logger.info(f"✅ 消息发送成功！消息ID: {message_obj.message_id}")
    return True
```

#### 步骤3: 集成到主流程
```python
# 在subscribe_new_tokens函数中
formatted_message = format_token_info(token_info)
success = await send_to_telegram(formatted_message, token_address)
if not success:
    logger.warning("发送到Telegram失败，将在日志中记录消息")
    logger.info(f"消息内容:\n{formatted_message}")
else:
    # 电报发送成功时，也写入交易信号文件
    await write_to_trading_bot_signal_file(token_address)
```

## 2. 遇到的问题和解决方案

### 2.1 多实例运行导致数据库锁定

**问题现象**:
```
2025-07-15 13:26:11,737 - telethon.client.updates - WARNING - Error executing high-level request after reconnect: <class 'sqlite3.OperationalError'>: database is locked
```

**根本原因**: 
- 同时运行了两个程序实例
- 两个实例尝试访问同一个SQLite会话文件
- SQLite不支持多进程同时写入

**解决方案**:
1. **立即解决**: 杀掉重复进程
2. **长期解决**: 实现多实例会话管理

```python
def get_session_name():
    """获取会话文件名，支持多实例但保持会话持久性"""
    # 方案1：基于启动参数
    if len(sys.argv) > 1:
        instance_id = sys.argv[1]
        session_name = f"telegram_session_{instance_id}"
        return session_name
    
    # 方案2：基于环境变量
    instance_id = os.environ.get('INSTANCE_ID')
    if instance_id:
        session_name = f"telegram_session_{instance_id}"
        return session_name
    
    # 方案3：默认使用固定名称
    return "telegram_search_session"
```

### 2.2 电报消息发送异步调用错误

**问题现象**:
```
'coroutine' object has no attribute 'message_id'
```

**根本原因**: 
- `bot.send_message()` 返回协程对象
- 代码试图直接访问 `.message_id` 属性
- 需要使用 `await` 调用

**解决方案**:
```python
# 修改前（错误）
message_obj = bot.send_message(chat_id=TELEGRAM_CHAT_ID, text=cleaned_message, parse_mode='Markdown')

# 修改后（正确）
message_obj = await bot.send_message(chat_id=TELEGRAM_CHAT_ID, text=cleaned_message, parse_mode='Markdown')
```

### 2.3 文件写入格式问题

**问题现象**:
代币地址被写在同一行，无法被正确识别：
```
3QLPsmKAhjyfvrp2nqScoTCyt87aAxvSPC2KGARipumpCaQF9oVKPevF9cvMKic5geJ9LaFBQyBhkubK9P9rx14o
DgvDb9gj7D12qTYYYppETJ9KdophfG9gfIHfTfxpump
```

**根本原因**: 
- 文件末尾没有换行符
- 新地址直接追加到上一行末尾

**初始解决方案（失败）**:
```python
# 使用 f.seek(-1, 2) 检查最后一个字符
f.seek(-1, 2)  # 这在某些系统上会失败
last_char = f.read(1)
```

**错误信息**:
```
can't do nonzero end-relative seeks
```

**最终解决方案**:
```python
# 先读取文件内容检查最后一个字符
file_needs_newline = False
try:
    if os.path.exists(signal_file_path):
        with open(signal_file_path, 'rb') as check_f:
            check_f.seek(0, 2)  # 移动到文件末尾
            if check_f.tell() > 0:  # 如果文件不为空
                check_f.seek(-1, 2)  # 移动到最后一个字节
                last_byte = check_f.read(1)
                if last_byte != b'\n':
                    file_needs_newline = True
except:
    pass  # 如果检查失败，继续执行

# 写入时根据需要添加换行符
with open(signal_file_path, 'a', encoding='utf-8') as f:
    if file_needs_newline:
        f.write('\n')
    f.write(f"{token_address}\n")
```

### 2.4 函数参数不一致错误

**问题现象**:
```
GmgnApiClient.log_pool_status() got an unexpected keyword argument 'force'
```

**根本原因**: 
- 修改了函数签名但忘记更新所有调用点
- 代码修改不一致导致的版本冲突

**解决方案**:
```python
# 统一所有调用点
gmgn_client.log_pool_status()  # 移除 force=True 参数
```

### 2.5 Unicode编码错误

**问题现象**:
```
UnicodeEncodeError: 'utf-8' codec can't encode characters in position 288-289: surrogates not allowed
```

**根本原因**: 
- 日志系统无法处理代理对字符（surrogate pairs）
- 某些Telegram消息包含特殊Unicode字符

**解决方案**:
```python
def clean_unicode_for_logging(text):
    """清理Unicode字符，移除代理对字符和其他问题字符"""
    if not isinstance(text, str):
        text = str(text)
    
    try:
        # 移除代理对字符
        cleaned = ''.join(char for char in text if not (0xD800 <= ord(char) <= 0xDFFF))
        
        # 标准化Unicode字符
        cleaned = unicodedata.normalize('NFKC', cleaned)
        
        # 移除控制字符（保留换行符和制表符）
        cleaned = ''.join(char for char in cleaned if unicodedata.category(char)[0] != 'C' or char in '\n\t\r')
        
        # 确保可以编码为UTF-8
        cleaned.encode('utf-8')
        
        return cleaned
    except Exception:
        return text.encode('utf-8', errors='ignore').decode('utf-8', errors='ignore')

class SafeFormatter(logging.Formatter):
    def format(self, record):
        # 清理日志消息中的Unicode字符
        if hasattr(record, 'msg') and isinstance(record.msg, str):
            record.msg = clean_unicode_for_logging(record.msg)
        
        # 清理参数中的Unicode字符
        if hasattr(record, 'args') and record.args:
            cleaned_args = []
            for arg in record.args:
                if isinstance(arg, str):
                    cleaned_args.append(clean_unicode_for_logging(arg))
                else:
                    cleaned_args.append(arg)
            record.args = tuple(cleaned_args)
        
        return super().format(record)
```

## 3. 经验教训和最佳实践

### 3.1 多实例运行管理

**经验教训**:
- 每次程序启动生成唯一会话文件会导致重复验证码输入
- 需要在支持多实例和保持会话持久性之间找到平衡

**最佳实践**:
```bash
# 单实例运行（默认）
python main.py

# 多实例运行（指定实例ID）
python main.py instance1
python main.py instance2

# 使用环境变量
set INSTANCE_ID=worker1 && python main.py
```

**代码模式**:
```python
def get_session_name():
    # 优先级：命令行参数 > 环境变量 > 默认值
    if len(sys.argv) > 1:
        return f"telegram_session_{sys.argv[1]}"

    instance_id = os.environ.get('INSTANCE_ID')
    if instance_id:
        return f"telegram_session_{instance_id}"

    return "telegram_search_session"  # 默认固定名称
```

### 3.2 异步函数调用管理

**经验教训**:
- 混合同步和异步函数容易出错
- 修改函数签名时必须检查所有调用点

**最佳实践**:
```python
# 1. 保持函数调用的一致性
async def send_to_telegram(message, token_address=None):
    # 异步函数内部使用 await
    message_obj = await bot.send_message(...)
    return True

# 2. 在异步上下文中调用
success = await send_to_telegram(formatted_message, token_address)

# 3. 错误处理
try:
    success = await send_to_telegram(formatted_message, token_address)
    if success:
        await write_to_trading_bot_signal_file(token_address)
except Exception as e:
    logger.error(f"处理失败: {e}")
```

### 3.3 文件操作安全性

**经验教训**:
- 文件追加操作需要考虑换行符问题
- 多进程写入需要文件锁定机制
- 不同操作系统的文件操作行为可能不同

**最佳实践**:
```python
def safe_append_to_file(file_path, content):
    """安全的文件追加操作"""
    try:
        # 1. 检查文件末尾是否需要换行符
        needs_newline = False
        if os.path.exists(file_path):
            with open(file_path, 'rb') as f:
                f.seek(0, 2)
                if f.tell() > 0:
                    f.seek(-1, 2)
                    if f.read(1) != b'\n':
                        needs_newline = True

        # 2. 使用文件锁定写入
        with open(file_path, 'a', encoding='utf-8') as f:
            try:
                import msvcrt  # Windows
                msvcrt.locking(f.fileno(), msvcrt.LK_LOCK, 1)
                if needs_newline:
                    f.write('\n')
                f.write(f"{content}\n")
                f.flush()
                msvcrt.locking(f.fileno(), msvcrt.LK_UNLCK, 1)
            except ImportError:
                # 非Windows系统的处理
                if needs_newline:
                    f.write('\n')
                f.write(f"{content}\n")
                f.flush()

        return True
    except Exception as e:
        logger.error(f"文件写入失败: {e}")
        return False
```

### 3.4 Unicode字符处理

**经验教训**:
- 日志系统对特殊Unicode字符敏感
- 代理对字符会导致编码错误
- 需要在数据处理的早期阶段清理字符

**最佳实践**:
```python
# 1. 创建安全的日志格式化器
class SafeFormatter(logging.Formatter):
    def format(self, record):
        # 清理所有字符串类型的数据
        if hasattr(record, 'msg') and isinstance(record.msg, str):
            record.msg = clean_unicode_for_logging(record.msg)

        if hasattr(record, 'args') and record.args:
            cleaned_args = []
            for arg in record.args:
                if isinstance(arg, str):
                    cleaned_args.append(clean_unicode_for_logging(arg))
                else:
                    cleaned_args.append(arg)
            record.args = tuple(cleaned_args)

        return super().format(record)

# 2. 在日志配置中使用安全格式化器
safe_formatter = SafeFormatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
console_handler.setFormatter(safe_formatter)
file_handler.setFormatter(safe_formatter)
```

### 3.5 错误处理和日志记录

**最佳实践**:
```python
async def robust_operation(operation_name, operation_func, *args, **kwargs):
    """通用的健壮操作包装器"""
    try:
        logger.info(f"开始执行: {operation_name}")
        result = await operation_func(*args, **kwargs)
        logger.info(f"✅ {operation_name} 执行成功")
        return result, True
    except Exception as e:
        logger.error(f"❌ {operation_name} 执行失败: {e}")
        return None, False

# 使用示例
result, success = await robust_operation(
    "发送Telegram消息",
    send_to_telegram,
    formatted_message,
    token_address
)

if success:
    await robust_operation(
        "写入交易信号文件",
        write_to_trading_bot_signal_file,
        token_address
    )
```

## 4. 预防措施

### 4.1 代码修改检查清单

**函数修改检查**:
- [ ] 检查函数签名变更是否影响所有调用点
- [ ] 确认异步/同步函数调用的一致性
- [ ] 验证参数类型和数量的匹配
- [ ] 检查返回值类型是否符合预期

**文件操作检查**:
- [ ] 确认文件路径的正确性（使用绝对路径）
- [ ] 验证文件编码设置（UTF-8）
- [ ] 检查文件锁定机制的实现
- [ ] 测试文件格式的正确性

**多实例运行检查**:
- [ ] 验证会话文件的唯一性
- [ ] 确认资源访问的互斥性
- [ ] 测试并发写入的安全性
- [ ] 检查进程间的数据一致性

### 4.2 测试流程和验证方法

**单元测试**:
```python
def test_write_to_signal_file():
    """测试信号文件写入功能"""
    test_file = "test_signals.txt"
    test_address = "TestTokenAddress123"

    # 清理测试文件
    if os.path.exists(test_file):
        os.remove(test_file)

    # 测试写入
    result = write_to_trading_bot_signal_file(test_address)
    assert result == True

    # 验证文件内容
    with open(test_file, 'r', encoding='utf-8') as f:
        content = f.read()
        assert test_address in content
        assert content.endswith('\n')

    # 清理
    os.remove(test_file)
```

**集成测试流程**:
1. **启动测试**: 启动单个实例，验证基本功能
2. **多实例测试**: 启动多个实例，验证并发安全性
3. **错误恢复测试**: 模拟各种错误情况，验证恢复机制
4. **长时间运行测试**: 运行数小时，验证稳定性

**验证方法**:
```bash
# 1. 检查进程状态
tasklist | findstr python

# 2. 验证文件格式
type "D:\meme trade bot\token_signals.txt"

# 3. 监控日志输出
tail -f token_aggregator.log

# 4. 测试多实例启动
python main.py instance1
python main.py instance2
```

### 4.3 常见陷阱和避免方法

**陷阱1: 热重载代码修改**
- **问题**: 程序运行时修改代码，新旧版本混合
- **避免**: 修改代码后必须重启程序

**陷阱2: 文件路径相对性**
- **问题**: 使用相对路径导致文件找不到
- **避免**: 始终使用绝对路径

**陷阱3: 异步函数调用遗漏**
- **问题**: 忘记在异步函数前加 await
- **避免**: 建立代码审查检查清单

**陷阱4: Unicode字符处理不当**
- **问题**: 特殊字符导致程序崩溃
- **避免**: 在数据处理早期阶段清理字符

**陷阱5: 多实例资源冲突**
- **问题**: 多个实例访问同一资源导致冲突
- **避免**: 实现适当的资源隔离和锁定机制

### 4.4 监控和维护建议

**日志监控**:
```python
# 关键指标监控
def log_system_health():
    """记录系统健康状态"""
    logger.info(f"📊 系统状态:")
    logger.info(f"   运行时间: {get_runtime()}")
    logger.info(f"   处理代币数: {processed_tokens_count}")
    logger.info(f"   成功发送数: {successful_sends_count}")
    logger.info(f"   文件写入数: {file_writes_count}")
    logger.info(f"   错误次数: {error_count}")
```

**定期维护任务**:
- 每日检查日志文件大小，必要时轮转
- 每周检查信号文件格式的正确性
- 每月清理过期的会话文件
- 定期更新API密钥和配置

**性能优化建议**:
- 使用连接池减少网络开销
- 实现适当的缓存机制
- 优化日志输出频率
- 监控内存使用情况

## 5. 总结

本次实现成功解决了两个系统间的数据对接问题，主要成果包括：

1. **功能实现**: 成功实现代币地址自动写入信号文件
2. **多实例支持**: 解决了多实例运行的会话管理问题
3. **错误处理**: 建立了完善的错误处理和恢复机制
4. **稳定性提升**: 解决了Unicode编码等稳定性问题

**关键成功因素**:
- 系统性的问题分析和解决
- 完善的错误处理机制
- 充分的测试和验证
- 详细的文档记录

**未来改进方向**:
- 实现更智能的连接池管理
- 添加更多的监控和告警机制
- 优化性能和资源使用
- 增强系统的可观测性

这个实现为类似的系统集成任务提供了宝贵的经验和可复用的解决方案。
