# 技术栈

## 编程语言
- **Python 3.10+**

## 主要依赖库
- **asyncio**: 异步编程
- **websockets**: WebSocket客户端连接
- **requests**: HTTP请求
- **telegram**: Telegram Bot API
- **telethon**: Telegram客户端API
- **curl_cffi**: GMGN API的HTTP请求（可选）
- **concurrent.futures**: 并发处理

## 外部API集成
- **PumpPortal WebSocket**: 新代币事件监控
- **Axiom API**: 代币开发者和持有者数据
- **Ave.ai API**: 代币标签统计和风险评估
- **GMGN API**: 代币统计和交易数据
- **SolanaTracker API**: 代币风险数据
- **DexPaid API**: 代币支付信息

## 数据存储
- **文件系统**: 配置文件、日志文件、会话文件
- **内存**: 实时数据处理

## 网络配置
- 支持代理配置（HTTP代理）
- 支持直连模式